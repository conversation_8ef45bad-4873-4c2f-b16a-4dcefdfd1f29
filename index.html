<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinancePro - Business Intelligence Platform</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💰</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, query, orderBy, where, deleteDoc, doc, updateDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged, updateProfile } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCTIP-YgAANJMLgls_T0CVCUhhVIbg0EZI",
            authDomain: "tokoexpensetracker.firebaseapp.com",
            projectId: "tokoexpensetracker",
            storageBucket: "tokoexpensetracker.firebasestorage.app",
            messagingSenderId: "1089193691554",
            appId: "1:1089193691554:web:26a80410d7094a5c794afb"
        };

        // Initialize Firebase with error handling
        try {
            const app = initializeApp(firebaseConfig);
            const db = getFirestore(app);
            const auth = getAuth(app);

            // Make Firebase available globally
            window.db = db;
            window.auth = auth;
            window.firebaseAuth = { signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged, updateProfile };
            window.firestore = { collection, addDoc, getDocs, query, orderBy, where, deleteDoc, doc, updateDoc };

            console.log('✅ Firebase initialized successfully');
        } catch (error) {
            console.log('⚠️ Firebase initialization failed:', error.message);
            console.log('📱 App will continue using localStorage only');

            // Set Firebase globals to null to indicate failure
            window.db = null;
            window.auth = null;
            window.firebaseAuth = null;
            window.firestore = null;
        }
    </script>
    
    <style>
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --bg-card: #1e1e1e;
            --bg-hover: #2a2a2a;
            --bg-glass: rgba(30, 30, 30, 0.8);
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --text-muted: #a1a1aa;
            --accent-primary: #3b82f6;
            --accent-secondary: #10b981;
            --accent-danger: #ef4444;
            --accent-warning: #f59e0b;
            --border-color: #27272a;
            --border-hover: #404040;
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
            --gradient-primary: linear-gradient(135deg, #3b82f6, #1d4ed8);
            --gradient-secondary: linear-gradient(135deg, #10b981, #059669);
            --gradient-danger: linear-gradient(135deg, #ef4444, #dc2626);
            --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
            --backdrop-blur: blur(12px);
        }

        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-card: #ffffff;
            --bg-hover: #f1f5f9;
            --bg-glass: rgba(255, 255, 255, 0.8);
            --text-primary: #1e293b;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .glass {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-xl);
        }

        @keyframes slideInFromLeft {
            from { 
                opacity: 0; 
                transform: translateX(-30px); 
            }
            to { 
                opacity: 1; 
                transform: translateX(0); 
            }
        }

        @keyframes scaleUp {
            from { 
                opacity: 0; 
                transform: scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: scale(1); 
            }
        }

        @keyframes pulse {
            0%, 100% { 
                transform: scale(1); 
                opacity: 1; 
            }
            50% { 
                transform: scale(1.05); 
                opacity: 0.8; 
            }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes borderGlow {
            0%, 100% { 
                box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
            }
            50% { 
                box-shadow: 0 0 20px rgba(245, 158, 11, 0.6), 0 0 40px rgba(245, 158, 11, 0.2);
            }
        }

        /* Premium Stats Cards */
        .premium-stat-card {
            background: rgba(17, 17, 17, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .premium-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            opacity: 0.6;
        }

        .premium-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
        }

        .premium-stat-value {
            font-family: 'Inter', monospace;
            font-weight: 900;
            line-height: 1;
            font-size: 1.75rem;
            margin: 0;
        }

        .premium-stat-label {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin: 0 0 0.5rem 0;
            color: #9ca3af;
        }

        .premium-icon-container {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
            }
        }

        .animate-fade-in-up { animation: fadeInUp 0.6s ease; }
        .animate-fade-in-left { animation: fadeInLeft 0.6s ease; }
        .animate-glow { animation: glow 2s infinite; }
        .animate-slide-in { animation: slideInFromLeft 0.4s ease; }
        .animate-scale-up { animation: scaleUp 0.3s ease; }

        /* Login Screen */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: all 0.5s ease;
        }

        .login-container.hidden {
            opacity: 0;
            visibility: hidden;
            transform: scale(0.95);
        }

        .login-card {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            margin: 1rem auto;
            box-shadow: var(--shadow-xl);
            animation: fadeInUp 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .login-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-logo h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 0.5rem;
        }

        .login-logo p {
            color: var(--text-muted);
            font-size: 1rem;
        }

        /* Register Screen */
        .register-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: all 0.5s ease;
            overflow-y: auto;
            padding: 2rem 0;
        }

        .register-container.show {
            display: flex;
        }

        .register-card {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            margin: 2rem;
            box-shadow: var(--shadow-xl);
            animation: fadeInUp 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .register-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        /* App Layout */
        .app-container {
            display: flex;
            min-height: 100vh;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .app-container.visible {
            opacity: 1;
        }

        /* SIMPLIFIED SIDEBAR DESIGN */
        .sidebar {
            width: 250px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar.mobile-hidden {
            transform: translateX(-100%);
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            transition: margin-left 0.3s ease;
        }

        .main-content.full-width {
            margin-left: 0;
        }

        /* Simple Header */
        .sidebar-header {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: var(--accent-primary);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 700;
            color: white;
        }

        .logo-text h1 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .logo-text p {
            font-size: 0.7rem;
            color: var(--text-muted);
            margin: 0;
        }

        /* Simple Navigation */
        .sidebar-nav {
            flex: 1;
            padding: 0.75rem 0;
        }

        .nav-section {
            margin-bottom: 1.25rem;
        }

        .nav-section-title {
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 1rem 0.5rem 1rem;
            padding-bottom: 0.25rem;
        }

        .nav-section ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        /* Simplified Navigation Links */
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.7rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            margin: 0 0.5rem;
            border-radius: 6px;
            width: calc(100% - 1rem);
        }

        .nav-link:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .nav-link.active {
            background: var(--accent-primary);
            color: white;
            font-weight: 600;
        }

        .nav-icon {
            width: 16px;
            height: 16px;
            opacity: 0.8;
            flex-shrink: 0;
        }

        .nav-link:hover .nav-icon,
        .nav-link.active .nav-icon {
            opacity: 1;
        }

        /* Simple Footer */
        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        /* Clean User Info */
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin-bottom: 0.75rem;
        }

        .user-avatar {
            width: 34px;
            height: 34px;
            background: var(--accent-primary);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .user-details {
            flex: 1;
            overflow: hidden;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }

        .user-email {
            font-size: 0.7rem;
            color: var(--text-muted);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }

        /* Clean Logout Button */
        .logout-btn-new {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.7rem 1rem;
            background: var(--accent-danger);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .logout-btn-new:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .logout-btn-new:active {
            transform: translateY(0);
        }

        /* Header */
        .header {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-md);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--bg-tertiary);
            border-radius: 30px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 1rem;
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: var(--gradient-primary);
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .theme-toggle.light::before {
            transform: translateX(30px);
            background: var(--gradient-warning);
        }

        /* Cards */
        .card {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
            position: relative;
        }

        .card:hover {
            border-color: var(--border-hover);
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        /* Stats Grid - FINAL IMPROVED LAYOUT */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 3rem;
            width: 100%;
        }

        .stat-card {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            max-width: 100%;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            border-color: var(--border-hover);
            box-shadow: var(--shadow-xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            width: 100%;
        }

        .stat-info {
            flex: 1;
            min-width: 0; /* Allow text to shrink */
        }

        .stat-info h3 {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .stat-info p {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--text-primary);
            margin: 0;
            line-height: 1.1;
            word-break: break-word;
            overflow-wrap: break-word;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            opacity: 0.8;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-left: 0.5rem;
        }

        .stat-card:hover .stat-icon {
            opacity: 1;
            transform: scale(1.05);
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            align-self: flex-start;
            margin-top: auto;
            white-space: nowrap;
        }

        .stat-change.positive {
            color: var(--accent-secondary);
            background: rgba(16, 185, 129, 0.1);
        }

        .stat-change.negative {
            color: var(--accent-danger);
            background: rgba(239, 68, 68, 0.1);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.5rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success { background: var(--gradient-secondary); }
        .btn-danger { background: var(--gradient-danger); }
        .btn-warning { background: var(--gradient-warning); }
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .btn-google {
            background: #ffffff !important;
            color: #374151 !important;
            border: 1px solid var(--border-color) !important;
            margin-bottom: 1.5rem;
        }

        .btn-google:hover {
            background: #f9fafb !important;
            color: #111827 !important;
            border-color: var(--border-hover) !important;
        }

        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 1rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-lg);
            transform: translateY(-1px);
        }

        .form-input.error {
            border-color: var(--accent-danger);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: var(--accent-secondary);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-error {
            color: var(--accent-danger);
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: none;
        }

        .form-success {
            color: var(--accent-secondary);
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: none;
        }

        /* Password Strength */
        .password-strength {
            margin-top: 0.5rem;
            display: none;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: var(--bg-tertiary);
            margin-bottom: 0.5rem;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .strength-weak { background: var(--accent-danger); }
        .strength-medium { background: var(--accent-warning); }
        .strength-strong { background: var(--accent-secondary); }

        .strength-text {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* Checkbox */
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-tertiary);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .checkbox:checked {
            background: var(--gradient-primary);
            border-color: var(--accent-primary);
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
            line-height: 1.4;
        }

        /* Divider */
        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
        }

        .divider-line {
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .divider-text {
            padding: 0 1rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Loading Spinner */
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: none;
        }

        /* Tables */
        .table-wrapper {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: var(--bg-secondary);
            padding: 1rem 1.5rem;
            text-align: left;
            font-size: 0.875rem;
            font-weight: 700;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border-color);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            cursor: pointer;
        }

        .table th:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .table td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .table tr:hover td {
            background: var(--bg-hover);
        }

        /* Tabs */
        .tab-btn {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
        }

        .tab-btn::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .tab-btn:hover {
            color: var(--text-primary);
            background: var(--bg-hover);
            border-radius: 8px 8px 0 0;
        }

        .tab-btn.active {
            color: var(--accent-primary);
        }

        .tab-btn.active::before {
            width: 100%;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.4s ease;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeInUp 0.6s ease;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 1.5rem;
            right: 1.5rem;
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow-xl);
            z-index: 9999;
            max-width: 400px;
            transform: translateX(100%);
            transition: all 0.5s ease;
            animation: fadeInUp 0.5s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            border-left: 4px solid var(--accent-secondary);
        }

        .notification-error {
            border-left: 4px solid var(--accent-danger);
        }

        .notification-warning {
            border-left: 4px solid var(--accent-warning);
        }

        /* Product Details */
        .product-details {
            display: none;
            margin-top: 1rem;
            padding: 1.5rem;
            background: var(--bg-glass);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            animation: fadeInUp 0.3s ease;
        }

        .product-details.visible {
            display: block;
        }

        .product-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .product-info-item {
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: 8px;
            text-align: center;
        }

        .product-info-label {
            font-size: 0.75rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .product-info-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .profit-indicator {
            color: var(--accent-secondary);
        }

        /* Charts Container */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .chart-card {
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        /* Links */
        .auth-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .auth-links p {
            color: var(--text-muted);
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .auth-link {
            color: var(--accent-primary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .auth-link:hover {
            color: var(--text-primary);
            text-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
        }

        /* Success Message */
        .success-message {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .success-icon {
            width: 64px;
            height: 64px;
            background: var(--gradient-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            animation: glow 2s infinite;
        }

        .success-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .success-subtitle {
            color: var(--text-muted);
            margin-bottom: 2rem;
        }

        /* Transaction History Specific Styles */
        .filters-section {
            padding: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .stats-section {
            padding: 1.5rem 2rem;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: 12px;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .type-income {
            background: rgba(16, 185, 129, 0.1);
            color: var(--accent-secondary);
        }

        .type-expense {
            background: rgba(239, 68, 68, 0.1);
            color: var(--accent-danger);
        }

        .type-packing {
            background: rgba(245, 158, 11, 0.1);
            color: var(--accent-warning);
        }

        .amount-positive {
            color: var(--accent-secondary);
            font-weight: 600;
        }

        .amount-negative {
            color: var(--accent-danger);
            font-weight: 600;
        }

        .amount-neutral {
            color: var(--accent-warning);
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-muted);
        }

        .empty-state svg {
            opacity: 0.3;
            margin-bottom: 1rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .pagination button {
            padding: 0.5rem 1rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
        }

        .pagination button.active {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: var(--bg-hover);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 9999;
            }

            .sidebar.mobile-show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-menu-btn {
                display: block !important;
            }

            /* IMPROVED MOBILE STATS GRID */
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
                min-height: 120px;
            }

            .stat-info h3 {
                font-size: 0.7rem;
            }

            .stat-info p {
                font-size: 1.5rem;
            }

            .stat-icon {
                width: 24px;
                height: 24px;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .header {
                padding: 1rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .login-card, .register-card {
                margin: 1rem;
                padding: 2rem;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .search-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .table-wrapper {
                overflow-x: auto;
            }

            .table {
                min-width: 800px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 100vw;
            }

            /* SINGLE COLUMN FOR VERY SMALL SCREENS */
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
                min-height: 100px;
            }

            .stat-info p {
                font-size: 1.25rem;
            }

            .nav-section-title {
                font-size: 0.7rem;
            }

            .nav-link {
                padding: 0.75rem;
                font-size: 0.875rem;
            }

            .nav-icon {
                width: 18px;
                height: 18px;
            }

            .user-name {
                font-size: 0.8rem;
            }

            .user-email {
                font-size: 0.7rem;
            }

            .logout-btn-new {
                padding: 0.75rem 1rem;
                font-size: 0.8rem;
            }
        }

        /* Tablet responsive - IMPROVED */
        @media (min-width: 769px) and (max-width: 1024px) {
            .sidebar {
                width: 260px;
            }

            .main-content {
                margin-left: 260px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.25rem;
            }

            .stat-card {
                min-height: 130px;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Large screen optimizations - IMPROVED */
        @media (min-width: 1440px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 2rem;
            }

            .stat-card {
                padding: 2rem;
                min-height: 160px;
            }

            .stat-info p {
                font-size: 2rem;
            }

            .nav-link {
                padding: 1rem;
                font-size: 0.9rem;
            }

            .nav-icon {
                width: 18px;
                height: 18px;
            }
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent-warning);
        }

        /* Utility Classes for Better Layout */
        .text-green { color: var(--accent-secondary) !important; }
        .text-red { color: var(--accent-danger) !important; }
        .text-blue { color: var(--accent-primary) !important; }
        .text-yellow { color: var(--accent-warning) !important; }

        /* Ensure proper text wrapping */
        .stat-info p {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        /* Better spacing for small screens */
        @media (max-width: 320px) {
            .stats-grid {
                gap: 0.5rem;
            }
            
            .stat-card {
                padding: 0.75rem;
                min-height: 90px;
            }
            
            .stat-info h3 {
                font-size: 0.65rem;
            }
            
            .stat-info p {
                font-size: 1.1rem;
            }
            
            .stat-icon {
                width: 20px;
                height: 20px;
            }
        }

        /* Packing Summary Mobile Responsive */
        @media (max-width: 768px) {
            #packingMonthlySummary [style*="grid-template-columns"] {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 0.75rem;
            }

            /* Packing Tracking Filter Controls */
            #tracking [style*="justify-content: space-between"] {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 1rem !important;
            }

            #tracking [style*="display: flex; gap: 1rem; flex-wrap: wrap"] {
                flex-direction: column !important;
                gap: 0.75rem !important;
            }

            #tracking .form-select,
            #tracking .form-input {
                width: 100% !important;
                min-width: auto !important;
            }
        }

        @media (max-width: 480px) {
            #packingMonthlySummary [style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
                gap: 0.5rem;
            }
            
            #packingMonthlySummary [style*="padding: 1.25rem"] {
                padding: 1rem !important;
            }
            
            #packingMonthlySummary p {
                font-size: 1.25rem !important;
            }
        }

        /* Glow Effect */
        .glow-effect {
            position: relative;
        }

        .glow-effect::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--gradient-primary);
            border-radius: inherit;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .glow-effect:hover::before {
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div class="login-container" id="loginContainer">
        <div class="login-card">
            <div class="login-logo">
                <h1>FinancePro</h1>
                <p>Business Intelligence Platform</p>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" id="loginEmail" required placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-input" id="loginPassword" required placeholder="••••••••">
                </div>
                
                <button type="submit" class="btn" style="width: 100%; margin-top: 1rem;">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Sign In
                </button>
            </form>
            
            <div class="auth-links">
                <p>Don't have an account?</p>
                <a href="javascript:void(0)" onclick="showRegister()" class="auth-link">Create New Account</a>
            </div>
            
            <div style="text-align: center; margin-top: 2rem; color: var(--text-muted); font-size: 0.875rem;">
                Demo: <EMAIL> / password123<br>
                <small>🎯 Complete Business Intelligence Platform</small>
            </div>
        </div>
    </div>

    <!-- Register Screen -->
    <div class="register-container" id="registerContainer">
        <div class="register-card glass">
            <div id="registerForm">
                <div class="login-logo">
                    <h1>FinancePro</h1>
                    <p>Create Your Business Account</p>
                </div>
                
                <form id="registrationForm">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" class="form-input" id="fullName" required placeholder="John Doe">
                        <div class="form-error" id="nameError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-input" id="email" required placeholder="<EMAIL>">
                        <div class="form-error" id="emailError"></div>
                        <div class="form-success" id="emailSuccess"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-input" id="password" required placeholder="••••••••">
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <div class="strength-text" id="strengthText">Enter a password</div>
                        </div>
                        <div class="form-error" id="passwordError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" class="form-input" id="confirmPassword" required placeholder="••••••••">
                        <div class="form-error" id="confirmError"></div>
                        <div class="form-success" id="confirmSuccess"></div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" class="checkbox" id="termsCheckbox" required>
                        <label for="termsCheckbox" class="checkbox-label">
                            I agree to the <a href="javascript:void(0)" onclick="showTerms()" class="auth-link">Terms of Service</a> and <a href="javascript:void(0)" onclick="showPrivacy()" class="auth-link">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn animate-glow" id="registerButton">
                        <div class="loading-spinner" id="loadingSpinner"></div>
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20" id="registerIcon">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                        <span id="buttonText">Create Account</span>
                    </button>
                </form>
                
                <div class="auth-links">
                    <p>Already have an account?</p>
                    <a href="javascript:void(0)" onclick="showLogin()" class="auth-link">Sign In Here</a>
                </div>
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="success-message">
                <div class="success-icon">
                    <svg width="32" height="32" fill="white" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h2 class="success-title">Account Created Successfully!</h2>
                <p class="success-subtitle">Redirecting to dashboard in <span id="countdown">2</span> seconds...</p>
                <button onclick="goToDashboard()" class="btn">Continue to Dashboard</button>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div class="app-container" id="appContainer">
        <!-- SIMPLIFIED SIDEBAR -->
        <nav class="sidebar" id="sidebar">
            <!-- Header -->
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">F</div>
                    <div class="logo-text">
                        <h1>FinancePro</h1>
                        <p>Business Intelligence</p>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul>
                        <li><button class="nav-link active" onclick="showSection('dashboard')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            Dashboard
                        </button></li>
                        <li><button class="nav-link" onclick="showSection('input')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                            </svg>
                            Input Keuangan
                        </button></li>
                        <li><button class="nav-link" onclick="showSection('history')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            Riwayat Transaksi
                        </button></li>
                        <li><button class="nav-link" onclick="showSection('tracking')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V8a1 1 0 012 0v-.001c0-.024 0-.049-.003-.075L13.586 5H16a1 1 0 110 2h-1.586l-2.293 2.293a1 1 0 01-.293.707V10a1 1 0 11-2 0v-.001c0-.024 0-.049.003-.075L7.414 7H4a1 1 0 01-1-1V4z" clip-rule="evenodd"/>
                            </svg>
                            Tracking Packing
                        </button></li>
                        <li><button class="nav-link" onclick="showSection('reports')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                            </svg>
                            Reports & Analytics
                        </button></li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <ul>
                        <li><button class="nav-link" onclick="showSection('settings')">
                            <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                            </svg>
                            Data Master
                        </button></li>
                    </ul>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <svg width="18" height="18" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="user-details">
                        <div class="user-name">Demo Admin</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                </div>
                <button class="logout-btn-new" onclick="logout()">
                    <svg width="14" height="14" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                    </svg>
                    Logout
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <div class="header-title">
                        <button class="mobile-menu-btn" onclick="toggleSidebar()">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                            </svg>
                        </button>
                        <div>
                            <h1 class="page-title" id="pageTitle">Dashboard</h1>
                            <p class="page-subtitle" id="pageSubtitle">Real-time business intelligence & analytics</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="theme-toggle" id="themeToggle" onclick="toggleTheme()"></button>
                        <button class="btn btn-secondary">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Export Data
                        </button>
                        <button class="btn glow-effect" onclick="handleQuickAdd()">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                            </svg>
                            Quick Add
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div style="padding: 2rem;">
                <!-- Dashboard Section -->
                <section id="dashboard" class="content-section active animate-fade-in-up">
                    <!-- Stats Grid - IMPROVED STRUCTURE -->
                    <div class="stats-grid">
                        <div class="stat-card" style="border-top: 3px solid var(--accent-secondary);">
                            <div class="stat-header">
                                <div class="stat-info">
                                    <h3>Total Revenue</h3>
                                    <p id="totalRevenueDash">Rp0</p>
                                </div>
                                <svg class="stat-icon" fill="#10b981" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="stat-change positive" id="revenueChange">+0%</div>
                        </div>
                        
                        <div class="stat-card" style="border-top: 3px solid var(--accent-danger);">
                            <div class="stat-header">
                                <div class="stat-info">
                                    <h3>Total Expenses</h3>
                                    <p id="totalExpenseDash">Rp0</p>
                                </div>
                                <svg class="stat-icon" fill="#ef4444" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="stat-change negative" id="expenseChange">+0%</div>
                        </div>
                        
                        <div class="stat-card" style="border-top: 3px solid var(--accent-primary);">
                            <div class="stat-header">
                                <div class="stat-info">
                                    <h3>Net Profit</h3>
                                    <p id="netProfitDash">Rp0</p>
                                </div>
                                <svg class="stat-icon" fill="#3b82f6" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="stat-change positive" id="profitChange">+0%</div>
                        </div>
                        
                        <div class="stat-card" style="border-top: 3px solid var(--accent-warning);">
                            <div class="stat-header">
                                <div class="stat-info">
                                    <h3>Profit Margin</h3>
                                    <p id="profitMarginDash">0%</p>
                                </div>
                                <svg class="stat-icon" fill="#f59e0b" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="stat-change positive" id="marginChange">Good</div>
                        </div>
                    </div>

                    <!-- Charts -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.25rem; font-weight: 700; color: var(--text-primary);">Monthly Revenue Trends</h3>
                            </div>
                            <div style="padding: 2rem; height: 350px;">
                                <canvas id="monthlyChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.25rem; font-weight: 700; color: var(--text-primary);">Expense Distribution</h3>
                            </div>
                            <div style="padding: 2rem; height: 350px;">
                                <canvas id="expenseChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- AI Insights -->
                    <div class="card" style="background: linear-gradient(135deg, var(--bg-card), var(--bg-secondary)); margin-top: 3rem;">
                        <div style="display: flex; align-items: center; gap: 1rem; padding: 2rem; border-bottom: 1px solid var(--border-color);">
                            <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20" style="color: var(--accent-primary);">
                                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                            </svg>
                            <div>
                                <div style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary);">AI Business Insights</div>
                                <div style="font-size: 0.875rem; color: var(--text-muted);">Smart recommendations powered by advanced analytics</div>
                            </div>
                        </div>
                        <div style="padding: 2rem;" id="aiInsights">
                            <div style="padding: 1.5rem; background: var(--bg-tertiary); border-radius: 12px; margin-bottom: 1rem; border-left: 4px solid var(--accent-primary);">
                                <p style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6; margin: 0;">💡 Welcome to FinancePro! Start adding transactions to get personalized business insights and recommendations.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Input Keuangan Section -->
                <section id="input" class="content-section" style="display: none;">
                    <div style="border-bottom: 1px solid var(--border-color); margin-bottom: 2rem;">
                        <div style="display: flex; gap: 1rem; margin-bottom: -1px;">
                            <button class="tab-btn active" onclick="showInputTab('income')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"/>
                                </svg>
                                Income
                            </button>
                            <button class="tab-btn" onclick="showInputTab('expense')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                Expenses
                            </button>
                            <button class="tab-btn" onclick="showInputTab('packing')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6a1 1 0 011-1h4a1 1 0 011 1v1a1 1 0 11-2 0V8a1 1 0 00-1-1H9a1 1 0 00-1 1v1a1 1 0 11-2 0z" clip-rule="evenodd"/>
                                </svg>
                                Packing
                            </button>
                        </div>
                    </div>

                    <!-- Income Tab -->
                    <div class="tab-content active" id="incomeTab">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Record Income</h3>
                                <p style="color: var(--text-muted);">Track all your business revenue streams</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="incomeForm">
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                                        <div class="form-group">
                                            <label class="form-label">Date</label>
                                            <input type="date" class="form-input" id="incomeDate" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Category</label>
                                            <select class="form-select" id="incomeCategory" required onchange="toggleProductFields()">
                                                <option value="">Select Category</option>
                                                <option value="Product Sales">Product Sales</option>
                                                <option value="Consulting Services">Consulting Services</option>
                                                <option value="Affiliate Commissions">Affiliate Commissions</option>
                                                <option value="Passive Income">Passive Income</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        
                                        <!-- Product Fields (Hidden by default) -->
                                        <div class="form-group" id="productField" style="display: none;">
                                            <label class="form-label">Product</label>
                                            <select class="form-select" id="incomeProduct" onchange="selectProduct()">
                                                <option value="">Select Product</option>
                                            </select>
                                        </div>
                                        <div class="form-group" id="qtyField" style="display: none;">
                                            <label class="form-label">Quantity Sold</label>
                                            <input type="number" class="form-input" id="incomeQty" min="1" step="1" placeholder="1" onchange="calculateIncomeTotal()">
                                        </div>
                                        <div class="form-group" id="priceField" style="display: none;">
                                            <label class="form-label">Unit Price (Rp)</label>
                                            <input type="number" class="form-input" id="incomePrice" readonly placeholder="Auto from product">
                                        </div>
                                        
                                        <!-- Manual Amount Field -->
                                        <div class="form-group" id="manualAmountField">
                                            <label class="form-label">Amount (Rp)</label>
                                            <input type="number" class="form-input" id="incomeAmountManual" min="0" step="1" placeholder="Enter amount">
                                        </div>
                                        
                                        <!-- Auto Total Field (Hidden by default) -->
                                        <div class="form-group" id="totalField" style="display: none;">
                                            <label class="form-label">Total Revenue (Rp)</label>
                                            <input type="number" class="form-input" id="incomeAmount" readonly placeholder="Auto calculated">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-textarea" id="incomeDescription" placeholder="Income description..."></textarea>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-success glow-effect" style="margin-top: 1.5rem;">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Save Income
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Tab -->
                    <div class="tab-content" id="expenseTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Record Expense</h3>
                                <p style="color: var(--text-muted);">Track all your business expenses</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="expenseForm">
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                                        <div class="form-group">
                                            <label class="form-label">Date</label>
                                            <input type="date" class="form-input" id="expenseDate" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Category</label>
                                            <select class="form-select" id="expenseCategory" required>
                                                <option value="">Select Category</option>
                                                <option value="Raw Materials">Raw Materials</option>
                                                <option value="Office Operations">Office Operations</option>
                                                <option value="Marketing & Promotion">Marketing & Promotion</option>
                                                <option value="Transport & Logistics">Transport & Logistics</option>
                                                <option value="Maintenance">Maintenance</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Amount (Rp)</label>
                                            <input type="number" class="form-input" id="expenseAmount" required min="0" step="1">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-textarea" id="expenseDescription" placeholder="Expense description..."></textarea>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-danger glow-effect" style="margin-top: 1.5rem;">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Save Expense
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Packing Tab -->
                    <div class="tab-content" id="packingTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Packing Materials Purchase</h3>
                                <p style="color: var(--text-muted);">Track packaging material expenses for cost analysis</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="packingForm">
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                                        <div class="form-group">
                                            <label class="form-label">Date</label>
                                            <input type="date" class="form-input" id="packingDate" required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Material</label>
                                            <select class="form-select" id="packingItem" required>
                                                <option value="">Select Material</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Quantity</label>
                                            <input type="number" class="form-input" id="packingQty" required min="1" step="1" onchange="calculatePackingTotal()">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Unit Price</label>
                                            <input type="number" class="form-input" id="packingPrice" required min="0" step="1" placeholder="Rp0" onchange="calculatePackingTotal()">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Total</label>
                                            <input type="number" class="form-input" id="packingTotal" readonly placeholder="Rp0">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-warning glow-effect" style="margin-top: 1.5rem;">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Save Purchase
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Transaction History Section -->
                <section id="history" class="content-section" style="display: none;">
                    <!-- Filters Section -->
                    <div class="card" style="margin-bottom: 2rem;">
                        <div class="filters-section">
                            <h3 style="margin-bottom: 1.5rem; color: var(--text-primary);">Filter & Search Transactions</h3>
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label class="form-label">Date From</label>
                                    <input type="date" class="form-input" id="historyDateFrom">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Date To</label>
                                    <input type="date" class="form-input" id="historyDateTo">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Transaction Type</label>
                                    <select class="form-select" id="historyTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="income">Income</option>
                                        <option value="expense">Expense</option>
                                        <option value="packing">Packing</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Category</label>
                                    <select class="form-select" id="historyCategoryFilter">
                                        <option value="">All Categories</option>
                                    </select>
                                </div>
                            </div>
                            <div class="search-actions">
                                <input type="text" class="form-input" placeholder="Search description, category, product..." style="flex: 1; min-width: 250px;" id="historySearchInput">
                                <button class="btn" onclick="applyHistoryFilters()">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                                    </svg>
                                    Search
                                </button>
                                <button class="btn btn-secondary" onclick="clearHistoryFilters()">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                    </svg>
                                    Clear
                                </button>
                                <button class="btn btn-secondary" onclick="exportHistoryTransactions()">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                    Export Data
                                </button>
                            </div>
                        </div>

                        <!-- Stats Section -->
                        <div class="stats-section">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-label">Total Transactions</div>
                                    <div class="stat-value" id="historyTotalCount">0</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Total Income</div>
                                    <div class="stat-value amount-positive" id="historyTotalIncome">Rp0</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Total Expenses</div>
                                    <div class="stat-value amount-negative" id="historyTotalExpenses">Rp0</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Net Amount</div>
                                    <div class="stat-value" id="historyNetAmount">Rp0</div>
                                </div>
                            </div>
                        </div>

                        <!-- Table Section -->
                        <div style="padding: 2rem;">
                            <div class="table-wrapper">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th onclick="sortHistoryBy('date')">Date</th>
                                            <th onclick="sortHistoryBy('type')">Type</th>
                                            <th onclick="sortHistoryBy('category')">Category</th>
                                            <th>Description</th>
                                            <th onclick="sortHistoryBy('amount')">Amount</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="historyTransactionsTable">
                                        <tr>
                                            <td colspan="6" style="text-align: center; color: var(--text-muted); padding: 2rem;">Loading transactions...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <div class="pagination" id="historyPagination">
                                <!-- Pagination buttons will be generated here -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Tracking Section -->
                <section id="tracking" class="content-section" style="display: none;">
                    <div class="card">
                        <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                            <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Packing Materials Tracking</h3>
                            <p style="color: var(--text-muted);">Monitor and analyze packaging material expenses</p>
                        </div>
                        <div style="padding: 2rem;">
                            <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; justify-content: space-between;">
                                <div style="display: flex; gap: 1rem; flex-wrap: wrap; align-items: center;">
                                    <select class="form-select" id="filterMonth" style="min-width: 160px;" onchange="updatePackingTable()">
                                        <option value="">All Months</option>
                                        <option value="1">January 2025</option>
                                        <option value="2">February 2025</option>
                                        <option value="3">March 2025</option>
                                        <option value="4">April 2025</option>
                                        <option value="5">May 2025</option>
                                        <option value="6">June 2025</option>
                                        <option value="7">July 2025</option>
                                        <option value="8">August 2025</option>
                                        <option value="9">September 2025</option>
                                        <option value="10">October 2025</option>
                                        <option value="11">November 2025</option>
                                        <option value="12">December 2025</option>
                                    </select>
                                    <select class="form-select" id="filterItem" style="min-width: 160px;" onchange="updatePackingTable()">
                                        <option value="">All Materials</option>
                                    </select>
                                    <input type="text" class="form-input" placeholder="Search transactions..." style="min-width: 200px;" onkeyup="searchPackingTable(this.value)" id="packingSearchInput">
                                </div>
                                <button class="btn btn-success" onclick="showPackingMonthlySummary()">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                                    </svg>
                                    Monthly Summary
                                </button>
                                <button class="btn btn-secondary" onclick="exportPackingData()">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                    Export Data
                                </button>
                            </div>

                            <!-- Monthly Summary Panel - PREMIUM REDESIGNED LAYOUT -->
                            <div id="packingMonthlySummary" style="display: none; margin-bottom: 2rem;">
                                <div style="background: linear-gradient(135deg, #1a1a1a, #2d2d2d); border: 2px solid #f59e0b; border-radius: 24px; padding: 2rem; box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
                                    <!-- Background Pattern -->
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%); pointer-events: none;"></div>
                                    
                                    <!-- Header -->
                                    <div style="position: relative; z-index: 2; display: flex; align-items: center; gap: 1rem; margin-bottom: 2rem;">
                                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(245, 158, 11, 0.3);">
                                            <span style="font-size: 24px;">📦</span>
                                        </div>
                                        <div>
                                            <h4 style="color: #ffffff; margin: 0; font-size: 1.75rem; font-weight: 800; letter-spacing: -0.025em;">Packing Cost Summary</h4>
                                            <p style="color: #9ca3af; margin: 0; font-size: 0.9rem;">Real-time packaging material analytics</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Stats Grid -->
                                    <div style="position: relative; z-index: 2; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                                        
                                        <!-- This Month Total -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #f59e0b; border-radius: 16px; padding: 1.5rem; position: relative; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">THIS MONTH TOTAL</h3>
                                                    <p id="packingThisMonth" style="font-size: 1.75rem; font-weight: 900; color: #f59e0b; margin: 0; line-height: 1; font-family: 'Inter', monospace;">Rp0</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(245, 158, 11, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">💰</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Total Transactions -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #3b82f6; border-radius: 16px; padding: 1.5rem; position: relative; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">TOTAL TRANSACTIONS</h3>
                                                    <p id="packingTransactionCount" style="font-size: 1.75rem; font-weight: 900; color: #3b82f6; margin: 0; line-height: 1; font-family: 'Inter', monospace;">0</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(59, 130, 246, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">📊</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Most Used Material -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #10b981; border-radius: 16px; padding: 1.5rem; position: relative; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">MOST USED MATERIAL</h3>
                                                    <p id="mostUsedMaterial" style="font-size: 1.25rem; font-weight: 800; color: #10b981; margin: 0; line-height: 1.2; font-family: 'Inter', sans-serif;">-</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(16, 185, 129, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">📦</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Avg Per Transaction -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #ef4444; border-radius: 16px; padding: 1.5rem; position: relative; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">AVG PER TRANSACTION</h3>
                                                    <p id="avgPackingPerDay" style="font-size: 1.75rem; font-weight: 900; color: #ef4444; margin: 0; line-height: 1; font-family: 'Inter', monospace;">Rp0</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">⏱️</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </div>
                                    
                                    <!-- Subtle animation glow -->
                                    <div style="position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(45deg, #f59e0b, #3b82f6, #10b981, #ef4444, #f59e0b); border-radius: 24px; z-index: -1; opacity: 0.1; animation: pulse 4s ease-in-out infinite;"></div>
                                </div>
                            </div>

                            <div class="table-wrapper">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Material</th>
                                            <th>Qty</th>
                                            <th>Unit Price</th>
                                            <th>Total</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="packingTable">
                                        <tr>
                                            <td colspan="6" style="text-align: center; color: var(--text-muted); padding: 2rem;">No packing transactions yet. Add some data!</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Reports Section -->
                <section id="reports" class="content-section" style="display: none;">
                    <div style="border-bottom: 1px solid var(--border-color); margin-bottom: 2rem;">
                        <div style="display: flex; gap: 1rem; margin-bottom: -1px; flex-wrap: wrap;">
                            <button class="tab-btn active" onclick="showReportTab('summary')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                </svg>
                                Summary
                            </button>
                            <button class="tab-btn" onclick="showReportTab('monthly')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"/>
                                </svg>
                                Monthly
                            </button>
                            <button class="tab-btn" onclick="showReportTab('products')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V8a1 1 0 012 0v-.001c0-.024 0-.049-.003-.075L13.586 5H16a1 1 0 110 2h-1.586l-2.293 2.293a1 1 0 01-.293.707V10a1 1 0 11-2 0v-.001c0-.024 0-.049.003-.075L7.414 7H4a1 1 0 01-1-1V4z" clip-rule="evenodd"/>
                                </svg>
                                Products
                            </button>
                        </div>
                    </div>

                    <!-- Summary Tab -->
                    <div class="tab-content active" id="summaryReportTab">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Financial Summary</h3>
                                <p style="color: var(--text-muted);">Complete overview of your business performance</p>
                            </div>
                            <div style="padding: 2rem;">
                                <div class="stats-grid">
                                    <div class="stat-card">
                                        <div class="stat-info">
                                            <h3>Total Transactions</h3>
                                            <p id="totalTransactions">0</p>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-info">
                                            <h3>Highest Revenue</h3>
                                            <p id="highestIncome">Rp0</p>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-info">
                                            <h3>Highest Expense</h3>
                                            <p id="highestExpense">Rp0</p>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-info">
                                            <h3>Average Daily Revenue</h3>
                                            <p id="avgDailyRevenue">Rp0</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Tab -->
                    <div class="tab-content" id="monthlyReportTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem;">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 1rem;">Monthly Report</h3>
                                <div style="margin-bottom: 2rem;">
                                    <select class="form-select" id="reportMonth" style="max-width: 300px;" onchange="generateMonthlyReport()">
                                        <option value="">Select Month</option>
                                        <option value="1">January 2025</option>
                                        <option value="2">February 2025</option>
                                        <option value="3">March 2025</option>
                                        <option value="4">April 2025</option>
                                        <option value="5">May 2025</option>
                                        <option value="6">June 2025</option>
                                        <option value="7">July 2025</option>
                                        <option value="8">August 2025</option>
                                        <option value="9">September 2025</option>
                                        <option value="10">October 2025</option>
                                        <option value="11">November 2025</option>
                                        <option value="12">December 2025</option>
                                    </select>
                                </div>
                                <div id="monthlyReportContent">
                                    <p style="color: var(--text-muted);">Select a month to generate detailed report</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Tab -->
                    <div class="tab-content" id="productsReportTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem;">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 1rem;">Product Performance</h3>
                                <div id="productReportContent">
                                    <div class="table-wrapper">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Cost (HPP)</th>
                                                    <th>Selling Price</th>
                                                    <th>Profit Margin</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productPerformanceTable">
                                                <!-- Data will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Settings/Data Master Section -->
                <section id="settings" class="content-section" style="display: none;">
                    <div style="border-bottom: 1px solid var(--border-color); margin-bottom: 2rem;">
                        <div style="display: flex; gap: 1rem; margin-bottom: -1px; flex-wrap: wrap;">
                            <button class="tab-btn active" onclick="showSettingTab('products')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01.293.707V8a1 1 0 012 0v-.001c0-.024 0-.049-.003-.075L13.586 5H16a1 1 0 110 2h-1.586l-2.293 2.293a1 1 0 01-.293.707V10a1 1 0 11-2 0v-.001c0-.024 0-.049.003-.075L7.414 7H4a1 1 0 01-1-1V4z" clip-rule="evenodd"/>
                                </svg>
                                Products
                            </button>
                            <button class="tab-btn" onclick="showSettingTab('materials')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6a1 1 0 011-1h4a1 1 0 011 1v1a1 1 0 11-2 0V8a1 1 0 00-1-1H9a1 1 0 00-1 1v1a1 1 0 11-2 0z" clip-rule="evenodd"/>
                                </svg>
                                Packing Materials
                            </button>
                            <button class="tab-btn" onclick="showSettingTab('income')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                Income Categories
                            </button>
                            <button class="tab-btn" onclick="showSettingTab('expense')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                Expense Categories
                            </button>
                            <button class="tab-btn" onclick="showSettingTab('backup')">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                                Data Management
                            </button>
                        </div>
                    </div>

                    <!-- Products Tab -->
                    <div class="tab-content active" id="productsTab">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Product Management</h3>
                                <p style="color: var(--text-muted);">Manage your products with cost and pricing information</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="productForm" style="margin-bottom: 2rem;">
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                        <div class="form-group">
                                            <label class="form-label">Product Name</label>
                                            <input type="text" class="form-input" id="newProductName" placeholder="Product name..." required>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Cost (HPP) - Rp</label>
                                            <input type="number" class="form-input" id="newProductCost" placeholder="0" min="0" step="1" required onchange="calculateProductMargin()">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Selling Price - Rp</label>
                                            <input type="number" class="form-input" id="newProductPrice" placeholder="0" min="0" step="1" required onchange="calculateProductMargin()">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">Profit Margin</label>
                                            <input type="text" class="form-input" id="newProductMargin" readonly placeholder="0%">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-success glow-effect">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                        </svg>
                                        Add Product
                                    </button>
                                </form>

                                <div class="table-wrapper">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Cost (HPP)</th>
                                                <th>Selling Price</th>
                                                <th>Profit</th>
                                                <th>Margin</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productsTable">
                                            <!-- Data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Materials Tab -->
                    <div class="tab-content" id="materialsTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Packing Materials Management</h3>
                                <p style="color: var(--text-muted);">Manage custom packing materials for your business</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="materialForm" style="margin-bottom: 2rem;">
                                    <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: end;">
                                        <div class="form-group" style="margin-bottom: 0;">
                                            <label class="form-label">Material Name</label>
                                            <input type="text" class="form-input" id="newMaterialName" placeholder="Enter material name..." required>
                                        </div>
                                        <button type="submit" class="btn btn-success glow-effect">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                            </svg>
                                            Add Material
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="table-wrapper">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Material Name</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="materialsTable">
                                            <!-- Data akan diisi via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Income Categories Tab -->
                    <div class="tab-content" id="incomeCategoriesTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Income Categories Management</h3>
                                <p style="color: var(--text-muted);">Manage custom income categories for your business transactions</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="incomeCategoryForm" style="margin-bottom: 2rem;">
                                    <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: end;">
                                        <div class="form-group" style="margin-bottom: 0;">
                                            <label class="form-label">Income Category Name</label>
                                            <input type="text" class="form-input" id="newIncomeCategoryName" placeholder="Enter income category name..." required>
                                        </div>
                                        <button type="submit" class="btn btn-success glow-effect">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                            </svg>
                                            Add Category
                                        </button>
                                    </div>
                                </form>

                                <div class="table-wrapper">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Category Name</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="incomeCategoriesTable">
                                            <!-- Data akan diisi via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Categories Tab -->
                    <div class="tab-content" id="expenseCategoriesTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin-bottom: 0.5rem;">Expense Categories Management</h3>
                                <p style="color: var(--text-muted);">Manage custom expense categories for your business transactions</p>
                            </div>
                            <div style="padding: 2rem;">
                                <form id="expenseCategoryForm" style="margin-bottom: 2rem;">
                                    <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: end;">
                                        <div class="form-group" style="margin-bottom: 0;">
                                            <label class="form-label">Expense Category Name</label>
                                            <input type="text" class="form-input" id="newExpenseCategoryName" placeholder="Enter expense category name..." required>
                                        </div>
                                        <button type="submit" class="btn btn-success glow-effect">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                            </svg>
                                            Add Category
                                        </button>
                                    </div>
                                </form>

                                <div class="table-wrapper">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Category Name</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="expenseCategoriesTable">
                                            <!-- Data akan diisi via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Tab -->
                    <div class="tab-content" id="backupTab" style="display: none;">
                        <div class="card">
                            <div style="padding: 2rem; border-bottom: 1px solid var(--border-color);">
                                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary);">Data Management</h3>
                                <p style="color: var(--text-muted);">Backup, restore, and manage your business data</p>
                            </div>
                            <div style="padding: 2rem;">
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                                    <button class="btn btn-success glow-effect" onclick="backupData()">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Backup Data
                                    </button>
                                    
                                    <div>
                                        <input type="file" id="restoreInput" accept=".json" style="display: none;" onchange="restoreData(this)">
                                        <button class="btn btn-secondary glow-effect" onclick="document.getElementById('restoreInput').click()">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                            Restore Data
                                        </button>
                                    </div>
                                    
                                    <button class="btn btn-danger glow-effect" onclick="resetAllData()">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                        </svg>
                                        Reset All Data
                                    </button>
                                </div>
                                
                                <div style="margin-top: 2rem; padding: 1.5rem; background: var(--bg-tertiary); border-radius: 12px; border-left: 4px solid var(--accent-primary);">
                                    <h4 style="color: var(--text-primary); margin-bottom: 0.5rem;">💾 Advanced Local Storage</h4>
                                    <p style="color: var(--text-muted); font-size: 0.875rem;">Your data is stored securely in your browser's local storage. Export backups regularly for safety.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script>
        // ============================================================================
        // GLOBAL DATA & VARIABLES
        // ============================================================================
        
        let appData = {
            products: [],
            incomeCategories: [
                { id: 1, name: 'Product Sales' },
                { id: 2, name: 'Consulting Services' },
                { id: 3, name: 'Affiliate Commissions' },
                { id: 4, name: 'Passive Income' },
                { id: 5, name: 'Other' }
            ],
            expenseCategories: [
                { id: 1, name: 'Raw Materials' },
                { id: 2, name: 'Office Operations' },
                { id: 3, name: 'Marketing & Promotion' },
                { id: 4, name: 'Transport & Logistics' },
                { id: 5, name: 'Maintenance' },
                { id: 6, name: 'Other' }
            ],
            packingItems: [
                { id: 1, name: 'Small Box' },
                { id: 2, name: 'Medium Box' },
                { id: 3, name: 'Large Box' },
                { id: 4, name: 'Bubble Wrap' },
                { id: 5, name: 'White Tape' },
                { id: 6, name: 'Fragile Tape' },
                { id: 7, name: 'Plastic Wrap' },
                { id: 8, name: 'Address Labels' },
                { id: 9, name: 'Brand Stickers' }
            ],
            transactions: {
                income: [],
                expense: [],
                packing: []
            }
        };
        
        let monthlyChart = null;
        let expenseChart = null;
        
        // Transaction History Variables
        let allTransactions = [];
        let filteredTransactions = [];
        let currentPage = 1;
        const itemsPerPage = 20;
        let sortField = 'date';
        let sortDirection = 'desc';

        // ============================================================================
        // UTILITY FUNCTIONS
        // ============================================================================
        
        function formatCurrency(amount) {
            const absAmount = Math.abs(amount || 0);
            const formatted = new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(absAmount);
            
            // Return with proper negative sign if amount is negative
            return amount < 0 ? `-${formatted}` : formatted;
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    try {
                        document.body.removeChild(notification);
                    } catch (e) {
                        // Notification might already be removed
                    }
                }, 300);
            }, 4000);
        }
        
        // ============================================================================
        // THEME MANAGEMENT
        // ============================================================================
        
        function toggleTheme() {
            const body = document.body;
            const themeToggles = document.querySelectorAll('.theme-toggle');
            const currentTheme = body.getAttribute('data-theme');
            
            if (currentTheme === 'light') {
                body.removeAttribute('data-theme');
                themeToggles.forEach(toggle => toggle.classList.remove('light'));
                localStorage.setItem('theme', 'dark');
            } else {
                body.setAttribute('data-theme', 'light');
                themeToggles.forEach(toggle => toggle.classList.add('light'));
                localStorage.setItem('theme', 'light');
            }
        }
        
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme');
            const themeToggles = document.querySelectorAll('.theme-toggle');
            
            if (savedTheme === 'light') {
                document.body.setAttribute('data-theme', 'light');
                themeToggles.forEach(toggle => toggle.classList.add('light'));
            }
        }

        // ============================================================================
        // AUTHENTICATION FUNCTIONS
        // ============================================================================
        
        async function handleLogin(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '🔄 Signing In...';
            submitBtn.disabled = true;

            try {
                // Try Firebase Auth first
                if (window.auth && window.firebaseAuth) {
                    try {
                        const userCredential = await window.firebaseAuth.signInWithEmailAndPassword(window.auth, email, password);
                        const user = userCredential.user;

                        localStorage.setItem('userData', JSON.stringify({
                            name: user.displayName || user.email.split('@')[0],
                            email: user.email,
                            uid: user.uid
                        }));
                        localStorage.setItem('isLoggedIn', 'true');

                        showNotification('✅ Login successful! Welcome back', 'success');

                        setTimeout(() => {
                            document.getElementById('loginContainer').classList.add('hidden');
                            document.getElementById('appContainer').classList.add('visible');
                            initializeApp();
                        }, 1000);

                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        return;
                    } catch (firebaseError) {
                        console.log('Firebase login failed, trying demo login:', firebaseError.message);
                    }
                }

                // Fallback to demo login
                if (email === '<EMAIL>' && password === 'password123') {
                    localStorage.setItem('userData', JSON.stringify({
                        name: 'Demo Admin',
                        email: email,
                        uid: 'demo_user'
                    }));
                    localStorage.setItem('isLoggedIn', 'true');

                    showNotification('✅ Login successful! Welcome back', 'success');

                    setTimeout(() => {
                        document.getElementById('loginContainer').classList.add('hidden');
                        document.getElementById('appContainer').classList.add('visible');
                        initializeApp();
                    }, 1000);
                } else {
                    showNotification('❌ Invalid credentials. Try demo: <EMAIL> / password123', 'error');
                }

            } catch (error) {
                console.error('Login error:', error);
                showNotification('❌ Login failed: ' + error.message, 'error');
            }

            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }

        async function logout() {
            if (confirm('Are you sure you want to logout?')) {
                try {
                    // Sign out from Firebase if available
                    if (window.auth && window.firebaseAuth) {
                        try {
                            await window.firebaseAuth.signOut(window.auth);
                            console.log('✅ Firebase logout successful');
                        } catch (firebaseError) {
                            console.log('Firebase logout failed:', firebaseError.message);
                        }
                    }

                    localStorage.removeItem('userData');
                    localStorage.removeItem('isLoggedIn');

                    // Hide all screens
                    document.getElementById('loginContainer').classList.remove('hidden');
                    document.getElementById('appContainer').classList.remove('visible');
                    document.getElementById('registerContainer').classList.remove('show');

                    // Clear form
                    document.getElementById('loginEmail').value = '';
                    document.getElementById('loginPassword').value = '';

                    showNotification('👋 Logged out successfully', 'success');

                } catch (error) {
                    console.error('Logout error:', error);
                    localStorage.clear();
                    location.reload();
                }
            }
        }

        // Registration Functions
        function showRegister() {
            document.getElementById('loginContainer').classList.add('hidden');
            document.getElementById('registerContainer').classList.add('show');
        }

        function showLogin() {
            document.getElementById('registerContainer').classList.remove('show');
            document.getElementById('loginContainer').classList.remove('hidden');
        }

        function goToDashboard() {
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('userData', JSON.stringify({
                name: 'Demo User',
                email: '<EMAIL>',
                uid: 'demo_user'
            }));
            document.getElementById('registerContainer').classList.remove('show');
            document.getElementById('appContainer').classList.add('visible');
            initializeApp();
        }

        function startCountdown() {
            let seconds = 2;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                seconds--;
                if (countdownElement) {
                    countdownElement.textContent = seconds;
                }
                
                if (seconds <= 0) {
                    clearInterval(timer);
                    goToDashboard();
                }
            }, 1000);
        }

        function showTerms() {
            alert('Terms of Service\n\nBy using FinancePro, you agree to our terms of service. Full terms will be available soon.');
        }

        function showPrivacy() {
            alert('Privacy Policy\n\nWe respect your privacy and protect your data. Full privacy policy will be available soon.');
        }

        // ============================================================================
        // NAVIGATION FUNCTIONS
        // ============================================================================
        
        function showSection(sectionName) {
            try {
                // Close mobile sidebar when navigation item is clicked
                if (window.innerWidth <= 768) {
                    const sidebar = document.getElementById('sidebar');
                    const overlay = document.getElementById('sidebar-overlay');
                    if (sidebar && sidebar.classList.contains('mobile-show')) {
                        sidebar.classList.remove('mobile-show');
                        if (overlay) overlay.remove();
                    }
                }

                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                    section.style.display = 'none';
                });
                
                const targetSection = document.getElementById(sectionName);
                if (targetSection) {
                    targetSection.classList.add('active');
                    targetSection.style.display = 'block';
                    targetSection.classList.add('animate-fade-in-up');
                }
                
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                
                const activeLink = document.querySelector(`[onclick*="${sectionName}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
                
                const titles = {
                    dashboard: { title: 'Dashboard', subtitle: 'Real-time business intelligence & analytics' },
                    input: { title: 'Financial Input', subtitle: 'Record all your business transactions efficiently' },
                    history: { title: 'Transaction History', subtitle: 'Complete record of all your business transactions' },
                    tracking: { title: 'Packing Tracking', subtitle: 'Monitor packaging material expenses and analytics' },
                    reports: { title: 'Reports & Analytics', subtitle: 'Comprehensive business insights and performance analysis' },
                    settings: { title: 'Data Master', subtitle: 'Manage categories and master data for your financial system' }
                };
                
                if (titles[sectionName]) {
                    const titleEl = document.getElementById('pageTitle');
                    const subtitleEl = document.getElementById('pageSubtitle');
                    if (titleEl) titleEl.textContent = titles[sectionName].title;
                    if (subtitleEl) subtitleEl.textContent = titles[sectionName].subtitle;
                }
                
                if (sectionName === 'dashboard') {
                    updateDashboard();
                    // Ensure expense chart is updated when dashboard is shown
                    setTimeout(() => updateExpenseChart(), 200);
                } else if (sectionName === 'history') {
                    loadTransactionHistory();
                } else if (sectionName === 'tracking') {
                    updatePackingTable();
                } else if (sectionName === 'reports') {
                    // FIX: Automatically update summary when reports section is opened
                    setTimeout(() => {
                        updateSummaryReport();
                        updateProductReport();
                    }, 50);
                } else if (sectionName === 'settings') {
                    setTimeout(() => {
                        loadProductsTable();
                        loadMaterialsTable();
                        loadIncomeCategoriesTable();
                        loadExpenseCategoriesTable();
                        updatePackingDropdowns();
                        updateProductDropdown();
                        updateCategoryDropdowns();
                    }, 50);
                }
            } catch (error) {
                console.error('Error showing section:', error);
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            if (sidebar) {
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('mobile-show');
                    
                    // Create or toggle overlay for mobile
                    if (sidebar.classList.contains('mobile-show')) {
                        if (!overlay) {
                            const newOverlay = document.createElement('div');
                            newOverlay.id = 'sidebar-overlay';
                            newOverlay.style.cssText = `
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(0, 0, 0, 0.5);
                                z-index: 999;
                                backdrop-filter: blur(4px);
                                transition: all 0.3s ease;
                            `;
                            newOverlay.onclick = toggleSidebar;
                            document.body.appendChild(newOverlay);
                        }
                    } else {
                        if (overlay) {
                            overlay.remove();
                        }
                    }
                }
            }
        }

        // ============================================================================
        // TAB MANAGEMENT FUNCTIONS
        // ============================================================================
        
        function showInputTab(tabName) {
            const tabs = document.querySelectorAll('#input .tab-content');
            tabs.forEach(tab => {
                tab.classList.remove('active');
                tab.style.display = 'none';
            });
            
            let targetTab;
            if (tabName === 'income') {
                targetTab = document.getElementById('incomeCategoriesTab');
            } else if (tabName === 'expense') {
                targetTab = document.getElementById('expenseCategoriesTab');
            } else {
                targetTab = document.getElementById(tabName + 'Tab');
            }

            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.display = 'block';
            }
            
            const tabBtns = document.querySelectorAll('#input .tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));
            
            const activeBtn = document.querySelector(`#input [onclick*="${tabName}"]`);
            if (activeBtn) activeBtn.classList.add('active');
        }

        function showReportTab(tabName) {
            const tabs = document.querySelectorAll('#reports .tab-content');
            tabs.forEach(tab => {
                tab.classList.remove('active');
                tab.style.display = 'none';
            });
            
            const targetTab = document.getElementById(tabName + 'ReportTab');
            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.display = 'block';
            }
            
            const tabBtns = document.querySelectorAll('#reports .tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));
            
            const activeBtn = document.querySelector(`#reports [onclick*="${tabName}"]`);
            if (activeBtn) activeBtn.classList.add('active');

            // FIX: Update reports immediately when tab is shown
            if (tabName === 'summary') {
                setTimeout(() => updateSummaryReport(), 10);
            }
            if (tabName === 'products') {
                setTimeout(() => updateProductReport(), 10);
            }
        }

        function showSettingTab(tabName) {
            const tabs = document.querySelectorAll('#settings .tab-content');
            tabs.forEach(tab => {
                tab.classList.remove('active');
                tab.style.display = 'none';
            });
            
            let targetTab;
            if (tabName === 'income') {
                targetTab = document.getElementById('incomeCategoriesTab');
            } else if (tabName === 'expense') {
                targetTab = document.getElementById('expenseCategoriesTab');
            } else {
                targetTab = document.getElementById(tabName + 'Tab');
            }

            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.display = 'block';
            }
            
            const tabBtns = document.querySelectorAll('#settings .tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));
            
            const activeBtn = document.querySelector(`#settings [onclick*="${tabName}"]`);
            if (activeBtn) activeBtn.classList.add('active');

            // Load data immediately when tab is opened
            if (tabName === 'products') loadProductsTable();
            if (tabName === 'materials') loadMaterialsTable();
            if (tabName === 'income') loadIncomeCategoriesTable();
            if (tabName === 'expense') loadExpenseCategoriesTable();
        }

        // ============================================================================
        // DATA MANAGEMENT FUNCTIONS
        // ============================================================================

        async function saveData() {
            try {
                // Save to localStorage as backup
                localStorage.setItem('financeProData', JSON.stringify(appData));

                // Save to Firebase
                if (window.db && window.firestore) {
                    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                    const userId = userData.uid || 'demo_user';

                    // Save each data type separately for better querying
                    await saveToFirebase('products', appData.products, userId);
                    await saveToFirebase('materials', appData.packingItems, userId);
                    await saveToFirebase('incomeCategories', appData.incomeCategories, userId);
                    await saveToFirebase('expenseCategories', appData.expenseCategories, userId);
                    await saveToFirebase('transactions', appData.transactions, userId);

                    console.log('💾 Data saved to Firebase and localStorage');
                } else {
                    console.log('💾 Data saved to localStorage only (Firebase not ready)');
                }
            } catch (error) {
                console.error('Error saving data:', error);
                showNotification('⚠️ Error saving data', 'error');
            }
        }

        async function saveToFirebase(collection, data, userId) {
            try {
                // Check if Firebase is properly initialized
                if (!window.db || !window.firestore) {
                    console.log('Firebase not initialized, skipping save');
                    return;
                }

                if (!data || Object.keys(data).length === 0) return;

                const docRef = await window.firestore.addDoc(window.firestore.collection(window.db, collection), {
                    userId: userId,
                    data: data,
                    timestamp: new Date(),
                    type: collection
                });

                console.log(`✅ ${collection} saved to Firebase with ID: ${docRef.id}`);
            } catch (error) {
                console.log(`Firebase save failed for ${collection}, using localStorage:`, error.message);
            }
        }

        async function loadData() {
            try {
                // Try to load from Firebase first
                if (window.db && window.firestore) {
                    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                    const userId = userData.uid || 'demo_user';

                    const firebaseData = await loadFromFirebase(userId);
                    if (firebaseData && Object.keys(firebaseData).length > 0) {
                        // Map Firebase data to correct appData structure
                        if (firebaseData.materials) {
                            appData.packingItems = firebaseData.materials;
                        }
                        if (firebaseData.products) {
                            appData.products = firebaseData.products;
                        }
                        if (firebaseData.incomeCategories) {
                            appData.incomeCategories = firebaseData.incomeCategories;
                        }
                        if (firebaseData.expenseCategories) {
                            appData.expenseCategories = firebaseData.expenseCategories;
                        }
                        if (firebaseData.transactions) {
                            appData.transactions = firebaseData.transactions;
                        }
                        console.log('📥 Data loaded from Firebase');
                        return Promise.resolve();
                    }
                }

                // Fallback to localStorage
                const savedData = localStorage.getItem('financeProData');
                if (savedData) {
                    try {
                        appData = JSON.parse(savedData);
                        console.log('📥 Data loaded from localStorage');
                    } catch (parseError) {
                        console.log('⚠️ localStorage data corrupted, using default data');
                        // Reset to default data if parsing fails
                        localStorage.removeItem('financeProData');
                    }
                }
                return Promise.resolve();
            } catch (error) {
                console.error('Error loading data:', error);
                return Promise.resolve();
            }
        }

        async function loadFromFirebase(userId) {
            try {
                const collections = ['products', 'materials', 'incomeCategories', 'expenseCategories', 'transactions'];
                const loadedData = {};

                for (const collectionName of collections) {
                    try {
                        // Try simple query first (without orderBy to avoid index requirement)
                        const q = window.firestore.query(
                            window.firestore.collection(window.db, collectionName),
                            window.firestore.where('userId', '==', userId)
                        );

                        const querySnapshot = await window.firestore.getDocs(q);
                        if (!querySnapshot.empty) {
                            // Get the most recent document by sorting in JavaScript
                            const docs = querySnapshot.docs.sort((a, b) => {
                                const aTime = a.data().timestamp?.toDate() || new Date(0);
                                const bTime = b.data().timestamp?.toDate() || new Date(0);
                                return bTime - aTime;
                            });

                            loadedData[collectionName] = docs[0].data().data;
                        }
                    } catch (collectionError) {
                        console.log(`Collection ${collectionName} not found or no access:`, collectionError.message);
                        // Continue with next collection
                    }
                }

                return loadedData;
            } catch (error) {
                console.error('Error loading from Firebase:', error);
                return {};
            }
        }

        // ============================================================================
        // TRANSACTION HISTORY FUNCTIONS
        // ============================================================================
        
        function loadTransactionHistory() {
            try {
                console.log('📊 Loading transaction history...');
                
                // Combine all transactions into one array
                allTransactions = [];
                
                // Add income transactions
                appData.transactions.income.forEach(transaction => {
                    allTransactions.push({
                        ...transaction,
                        type: 'income',
                        displayAmount: transaction.amount,
                        actualAmount: transaction.amount
                    });
                });
                
                // Add expense transactions
                appData.transactions.expense.forEach(transaction => {
                    allTransactions.push({
                        ...transaction,
                        type: 'expense',
                        displayAmount: -transaction.amount,
                        actualAmount: transaction.amount
                    });
                });
                
                // Add packing transactions
                appData.transactions.packing.forEach(transaction => {
                    allTransactions.push({
                        ...transaction,
                        type: 'packing',
                        category: 'Packing Materials',
                        displayAmount: -transaction.total,
                        actualAmount: transaction.total,
                        description: transaction.description || `${transaction.qty}x ${transaction.item} @ ${formatCurrency(transaction.price)}`
                    });
                });
                
                // Sort by date (newest first)
                allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));
                
                // Initialize filters
                filteredTransactions = [...allTransactions];
                currentPage = 1;
                
                // Update category filter options
                updateHistoryCategoryFilter();
                
                // Render table
                renderTransactionTable();
                updateHistoryStats();
                updateHistoryPagination();
                
                console.log(`✅ Loaded ${allTransactions.length} transactions`);
                
            } catch (error) {
                console.error('Error loading transaction history:', error);
                showNotification('❌ Error loading transaction history', 'error');
            }
        }

        function updateHistoryCategoryFilter() {
            const categoryFilter = document.getElementById('historyCategoryFilter');
            if (!categoryFilter) return;
            
            const categories = new Set();
            allTransactions.forEach(t => {
                if (t.category) categories.add(t.category);
            });
            
            const currentValue = categoryFilter.value;
            categoryFilter.innerHTML = '<option value="">All Categories</option>';
            
            [...categories].sort().forEach(category => {
                categoryFilter.innerHTML += `<option value="${category}">${category}</option>`;
            });
            
            categoryFilter.value = currentValue;
        }

        function renderTransactionTable() {
            const tbody = document.getElementById('historyTransactionsTable');
            if (!tbody) return;
            
            if (filteredTransactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20" style="opacity: 0.3; margin-bottom: 1rem;">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1z" clip-rule="evenodd"/>
                            </svg>
                            <div>No transactions found matching your criteria</div>
                            <small>Try adjusting your filters or add some transactions</small>
                        </td>
                    </tr>
                `;
                return;
            }
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageTransactions = filteredTransactions.slice(startIndex, endIndex);
            
            tbody.innerHTML = pageTransactions.map(transaction => {
                const typeClass = transaction.type === 'income' ? 'type-income' : 
                                transaction.type === 'expense' ? 'type-expense' : 'type-packing';
                const amountClass = transaction.displayAmount > 0 ? 'amount-positive' : 
                                  transaction.displayAmount < 0 ? 'amount-negative' : 'amount-neutral';
                
                return `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('id-ID')}</td>
                        <td><span class="type-badge ${typeClass}">${transaction.type}</span></td>
                        <td>${transaction.category || '-'}</td>
                        <td>${transaction.description || '-'}</td>
                        <td class="${amountClass}">${formatCurrency(Math.abs(transaction.displayAmount))}</td>
                        <td>
                            <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.75rem;" 
                                    onclick="deleteTransaction('${transaction.type}', ${transaction.id})">
                                Delete
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function updateHistoryStats() {
            const totalCount = filteredTransactions.length;
            const totalIncome = filteredTransactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.actualAmount, 0);
            const totalExpenses = filteredTransactions
                .filter(t => t.type === 'expense' || t.type === 'packing')
                .reduce((sum, t) => sum + t.actualAmount, 0);
            const netAmount = totalIncome - totalExpenses;
            
            const elements = {
                historyTotalCount: totalCount.toString(),
                historyTotalIncome: formatCurrency(totalIncome),
                historyTotalExpenses: formatCurrency(totalExpenses),
                historyNetAmount: formatCurrency(netAmount)
            };
            
            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    if (id === 'historyNetAmount') {
                        element.className = netAmount >= 0 ? 'stat-value amount-positive' : 'stat-value amount-negative';
                    }
                }
            });
        }

        function updateHistoryPagination() {
            const pagination = document.getElementById('historyPagination');
            if (!pagination) return;
            
            const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changeHistoryPage(${currentPage - 1})">Previous</button>`;
            }
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const activeClass = i === currentPage ? 'active' : '';
                    paginationHTML += `<button class="${activeClass}" onclick="changeHistoryPage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `<span>...</span>`;
                }
            }
            
            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changeHistoryPage(${currentPage + 1})">Next</button>`;
            }
            
            pagination.innerHTML = paginationHTML;
        }

        function changeHistoryPage(page) {
            currentPage = page;
            renderTransactionTable();
            updateHistoryPagination();
        }

        function applyHistoryFilters() {
            const dateFrom = document.getElementById('historyDateFrom').value;
            const dateTo = document.getElementById('historyDateTo').value;
            const typeFilter = document.getElementById('historyTypeFilter').value;
            const categoryFilter = document.getElementById('historyCategoryFilter').value;
            const searchInput = document.getElementById('historySearchInput').value.toLowerCase();
            
            filteredTransactions = allTransactions.filter(transaction => {
                // Date filters
                if (dateFrom && new Date(transaction.date) < new Date(dateFrom)) return false;
                if (dateTo && new Date(transaction.date) > new Date(dateTo)) return false;
                
                // Type filter
                if (typeFilter && transaction.type !== typeFilter) return false;
                
                // Category filter
                if (categoryFilter && transaction.category !== categoryFilter) return false;
                
                // Search filter
                if (searchInput) {
                    const searchFields = [
                        transaction.description || '',
                        transaction.category || '',
                        transaction.productName || '',
                        transaction.item || ''
                    ].join(' ').toLowerCase();
                    
                    if (!searchFields.includes(searchInput)) return false;
                }
                
                return true;
            });
            
            currentPage = 1;
            renderTransactionTable();
            updateHistoryStats();
            updateHistoryPagination();
            
            showNotification(`✅ Found ${filteredTransactions.length} transactions`, 'success');
        }

        function clearHistoryFilters() {
            document.getElementById('historyDateFrom').value = '';
            document.getElementById('historyDateTo').value = '';
            document.getElementById('historyTypeFilter').value = '';
            document.getElementById('historyCategoryFilter').value = '';
            document.getElementById('historySearchInput').value = '';
            
            filteredTransactions = [...allTransactions];
            currentPage = 1;
            
            renderTransactionTable();
            updateHistoryStats();
            updateHistoryPagination();
            
            showNotification('✅ Filters cleared', 'success');
        }

        function exportHistoryTransactions() {
            try {
                if (filteredTransactions.length === 0) {
                    showNotification('⚠️ No transactions to export', 'warning');
                    return;
                }
                
                const csvData = [
                    ['Date', 'Type', 'Category', 'Description', 'Amount'],
                    ...filteredTransactions.map(t => [
                        t.date,
                        t.type,
                        t.category || '',
                        t.description || '',
                        t.displayAmount
                    ])
                ];
                
                const csvContent = csvData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                
                window.URL.revokeObjectURL(url);
                showNotification('✅ Transactions exported successfully!', 'success');
                
            } catch (error) {
                console.error('Export error:', error);
                showNotification('❌ Export failed', 'error');
            }
        }

        function sortHistoryBy(field) {
            if (sortField === field) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortField = field;
                sortDirection = 'desc';
            }
            
            filteredTransactions.sort((a, b) => {
                let aVal = a[field];
                let bVal = b[field];
                
                if (field === 'date') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                } else if (field === 'amount') {
                    aVal = a.displayAmount;
                    bVal = b.displayAmount;
                }
                
                if (sortDirection === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });
            
            renderTransactionTable();
            updateHistoryPagination();
        }

        function deleteTransaction(type, id) {
            if (!confirm('Are you sure you want to delete this transaction?')) return;
            
            try {
                // Remove from appData
                const index = appData.transactions[type].findIndex(t => t.id === id);
                if (index !== -1) {
                    appData.transactions[type].splice(index, 1);
                    saveData();
                    
                    // Reload transaction history
                    loadTransactionHistory();
                    
                    // Update dashboard if visible
                    const dashboardSection = document.getElementById('dashboard');
                    if (dashboardSection && dashboardSection.classList.contains('active')) {
                        updateDashboard();
                    }
                    
                    showNotification('✅ Transaction deleted successfully!', 'success');
                }
            } catch (error) {
                console.error('Delete error:', error);
                showNotification('❌ Failed to delete transaction', 'error');
            }
        }

        // ============================================================================
        // PACKING TRACKING FUNCTIONS
        // ============================================================================
        
        function updatePackingTable() {
            const tbody = document.getElementById('packingTable');
            if (!tbody) return;
            
            let packingData = [...appData.transactions.packing];
            
            // Apply filters
            const monthFilter = document.getElementById('filterMonth').value;
            const itemFilter = document.getElementById('filterItem').value;
            
            if (monthFilter) {
                packingData = packingData.filter(transaction => {
                    const transactionMonth = new Date(transaction.date).getMonth() + 1;
                    return transactionMonth === parseInt(monthFilter);
                });
            }
            
            if (itemFilter) {
                packingData = packingData.filter(transaction => transaction.item === itemFilter);
            }
            
            if (packingData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; color: var(--text-muted); padding: 2rem;">
                            No packing transactions found
                        </td>
                    </tr>
                `;
                return;
            }
            
            // Sort by date (newest first)
            packingData.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            tbody.innerHTML = packingData.map(transaction => `
                <tr>
                    <td>${new Date(transaction.date).toLocaleDateString('id-ID')}</td>
                    <td>${transaction.item}</td>
                    <td>${transaction.qty}</td>
                    <td>${formatCurrency(transaction.price)}</td>
                    <td style="color: var(--accent-warning); font-weight: 600;">${formatCurrency(transaction.total)}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.75rem;" 
                                onclick="deleteTransaction('packing', ${transaction.id})">
                            Delete
                        </button>
                    </td>
                </tr>
            `).join('');
            
            console.log(`✅ Updated packing table with ${packingData.length} transactions`);
        }

        function searchPackingTable(searchTerm) {
            const tbody = document.getElementById('packingTable');
            if (!tbody) return;
            
            let packingData = [...appData.transactions.packing];
            
            if (searchTerm) {
                const term = searchTerm.toLowerCase();
                packingData = packingData.filter(transaction => 
                    transaction.item.toLowerCase().includes(term) ||
                    transaction.date.includes(term) ||
                    transaction.total.toString().includes(term)
                );
            }
            
            // Apply other filters too
            const monthFilter = document.getElementById('filterMonth').value;
            const itemFilter = document.getElementById('filterItem').value;
            
            if (monthFilter) {
                packingData = packingData.filter(transaction => {
                    const transactionMonth = new Date(transaction.date).getMonth() + 1;
                    return transactionMonth === parseInt(monthFilter);
                });
            }
            
            if (itemFilter) {
                packingData = packingData.filter(transaction => transaction.item === itemFilter);
            }
            
            if (packingData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; color: var(--text-muted); padding: 2rem;">
                            No transactions found for "${searchTerm}"
                        </td>
                    </tr>
                `;
                return;
            }
            
            packingData.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            tbody.innerHTML = packingData.map(transaction => `
                <tr>
                    <td>${new Date(transaction.date).toLocaleDateString('id-ID')}</td>
                    <td>${transaction.item}</td>
                    <td>${transaction.qty}</td>
                    <td>${formatCurrency(transaction.price)}</td>
                    <td style="color: var(--accent-warning); font-weight: 600;">${formatCurrency(transaction.total)}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.75rem;" 
                                onclick="deleteTransaction('packing', ${transaction.id})">
                            Delete
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function showPackingMonthlySummary() {
            const summaryDiv = document.getElementById('packingMonthlySummary');
            if (!summaryDiv) return;
            
            const isVisible = summaryDiv.style.display !== 'none';
            
            if (isVisible) {
                summaryDiv.style.display = 'none';
                return;
            }
            
            // Calculate monthly summary
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();
            
            const monthlyTransactions = appData.transactions.packing.filter(transaction => {
                const transactionDate = new Date(transaction.date);
                return transactionDate.getMonth() + 1 === currentMonth && 
                       transactionDate.getFullYear() === currentYear;
            });
            
            const monthlyTotal = monthlyTransactions.reduce((sum, t) => sum + t.total, 0);
            const transactionCount = monthlyTransactions.length;
            
            // Find most used material
            const materialCount = {};
            monthlyTransactions.forEach(transaction => {
                materialCount[transaction.item] = (materialCount[transaction.item] || 0) + transaction.qty;
            });
            
            const mostUsedMaterial = Object.keys(materialCount).length > 0 
                ? Object.keys(materialCount).reduce((a, b) => materialCount[a] > materialCount[b] ? a : b)
                : 'No data';
            
            // Calculate average per transaction (more meaningful than per day)
            const avgPerTransaction = transactionCount > 0 ? monthlyTotal / transactionCount : 0;
            
            // Update elements
            const elements = {
                packingThisMonth: formatCurrency(monthlyTotal),
                packingTransactionCount: transactionCount.toString(),
                mostUsedMaterial: mostUsedMaterial,
                avgPackingPerDay: formatCurrency(avgPerTransaction)
            };
            
            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) element.textContent = value;
            });
            
            summaryDiv.style.display = 'block';
            showNotification('📦 Monthly summary updated!', 'success');
        }

        // ============================================================================
        // REPORTS FUNCTIONS - FIXED VERSION
        // ============================================================================
        
        function updateSummaryReport() {
            try {
                console.log('🔧 Updating summary report...');
                
                const totalTransactions = 
                    appData.transactions.income.length + 
                    appData.transactions.expense.length + 
                    appData.transactions.packing.length;
                
                const highestIncome = appData.transactions.income.length > 0 
                    ? Math.max(...appData.transactions.income.map(t => t.amount))
                    : 0;
                
                const highestExpense = appData.transactions.expense.length > 0 
                    ? Math.max(...appData.transactions.expense.map(t => t.amount))
                    : 0;
                
                // Fix calculation for avgDailyRevenue - use actual days with income transactions
                const totalRevenue = appData.transactions.income.reduce((sum, t) => sum + t.amount, 0);
                const uniqueDates = new Set(appData.transactions.income.map(t => t.date));
                const totalDays = uniqueDates.size > 0 ? uniqueDates.size : 1; // Prevent division by zero
                const avgDailyRevenue = totalRevenue / totalDays;
                
                // Fix: Use direct element references and check if they exist
                const elementUpdates = {
                    totalTransactions: totalTransactions.toString(),
                    highestIncome: formatCurrency(highestIncome),
                    highestExpense: formatCurrency(highestExpense),
                    avgDailyRevenue: formatCurrency(avgDailyRevenue)
                };
                
                // Update elements with safer approach
                Object.entries(elementUpdates).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                        console.log(`✅ Updated ${id}: ${value}`);
                    } else {
                        console.warn(`⚠️ Element ${id} not found`);
                    }
                });
                
                console.log('✅ Summary report updated successfully');
                
            } catch (error) {
                console.error('❌ Error updating summary report:', error);
                showNotification('⚠️ Error updating summary report', 'error');
            }
        }

        function updateProductReport() {
            const tbody = document.getElementById('productPerformanceTable');
            if (!tbody) {
                console.warn('⚠️ Product performance table not found');
                return;
            }
            
            try {
                if (appData.products.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; color: var(--text-muted); padding: 2rem;">
                                No products available. Add products in Data Master section.
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                tbody.innerHTML = appData.products.map(product => {
                    const status = product.margin > 30 ? 'Excellent' : 
                                  product.margin > 15 ? 'Good' : 
                                  product.margin > 5 ? 'Fair' : 'Low';
                    
                    const statusClass = product.margin > 30 ? 'amount-positive' : 
                                       product.margin > 15 ? 'amount-neutral' : 'amount-negative';
                    
                    return `
                        <tr>
                            <td style="font-weight: 600;">${product.name}</td>
                            <td>${formatCurrency(product.cost)}</td>
                            <td>${formatCurrency(product.price)}</td>
                            <td style="color: var(--accent-secondary); font-weight: 600;">${product.margin.toFixed(1)}%</td>
                            <td><span class="${statusClass}" style="font-weight: 600;">${status}</span></td>
                        </tr>
                    `;
                }).join('');
                
                console.log('✅ Product report updated');
                
            } catch (error) {
                console.error('❌ Error updating product report:', error);
            }
        }

        function generateMonthlyReport() {
            const monthSelect = document.getElementById('reportMonth');
            const contentDiv = document.getElementById('monthlyReportContent');
            
            if (!monthSelect || !contentDiv) {
                console.warn('❌ Monthly report elements not found');
                return;
            }
            
            const selectedMonth = parseInt(monthSelect.value);
            
            if (!selectedMonth) {
                contentDiv.innerHTML = '<div style="padding: 2rem; text-align: center; color: var(--text-muted); font-size: 1rem;">📅 Select a month above to generate detailed financial report</div>';
                return;
            }
            
            try {
                console.log('📊 Generating monthly report for month:', selectedMonth);
                
                // Filter transactions by month (current year 2025)
                const currentYear = 2025;
                
                const monthlyIncome = appData.transactions.income.filter(transaction => {
                    const transactionDate = new Date(transaction.date);
                    const transactionMonth = transactionDate.getMonth() + 1;
                    const transactionYear = transactionDate.getFullYear();
                    return transactionMonth === selectedMonth && transactionYear === currentYear;
                });
                
                const monthlyExpenses = appData.transactions.expense.filter(transaction => {
                    const transactionDate = new Date(transaction.date);
                    const transactionMonth = transactionDate.getMonth() + 1;
                    const transactionYear = transactionDate.getFullYear();
                    return transactionMonth === selectedMonth && transactionYear === currentYear;
                });
                
                const monthlyPacking = appData.transactions.packing.filter(transaction => {
                    const transactionDate = new Date(transaction.date);
                    const transactionMonth = transactionDate.getMonth() + 1;
                    const transactionYear = transactionDate.getFullYear();
                    return transactionMonth === selectedMonth && transactionYear === currentYear;
                });
                
                // Calculate totals
                const totalIncome = monthlyIncome.reduce((sum, t) => sum + (t.amount || 0), 0);
                const totalExpenses = monthlyExpenses.reduce((sum, t) => sum + (t.amount || 0), 0);
                const totalPacking = monthlyPacking.reduce((sum, t) => sum + (t.total || 0), 0);
                const totalAllExpenses = totalExpenses + totalPacking;
                const netProfit = totalIncome - totalAllExpenses;
                const profitMargin = totalIncome > 0 ? ((netProfit / totalIncome) * 100) : 0;
                
                const monthNames = [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ];
                
                const monthName = monthNames[selectedMonth - 1];
                const totalTransactions = monthlyIncome.length + monthlyExpenses.length + monthlyPacking.length;
                
                console.log('📈 Monthly report data:', {
                    month: monthName,
                    totalIncome,
                    totalExpenses: totalAllExpenses,
                    netProfit,
                    totalTransactions
                });
                
                // Generate report HTML
                contentDiv.innerHTML = `
                            <div style="background: linear-gradient(135deg, #1a1a1a, #2d2d2d); border: 2px solid #3b82f6; border-radius: 24px; padding: 2rem; box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
                                    <!-- Background Pattern -->
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%); pointer-events: none;"></div>
                                    
                                    <!-- Header -->
                                    <div style="position: relative; z-index: 2; text-align: center; margin-bottom: 2rem;">
                                        <div style="display: inline-flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                            <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);">
                                                <span style="font-size: 24px;">📊</span>
                                            </div>
                                            <h4 style="color: #ffffff; margin: 0; font-size: 1.75rem; font-weight: 800; letter-spacing: -0.025em;">
                                                ${monthName} 2025 Financial Report
                                            </h4>
                                        </div>
                                        <p style="color: #9ca3af; margin: 0; font-size: 0.9rem;">
                                            Complete financial overview for ${monthName}
                                        </p>
                                    </div>
                                    
                                    <!-- Stats Grid -->
                                    <div style="position: relative; z-index: 2; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                                        
                                        <!-- Total Income -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #10b981; border-radius: 16px; padding: 1.5rem; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">TOTAL INCOME</h3>
                                                    <p style="font-size: 1.75rem; font-weight: 900; color: #10b981; margin: 0; line-height: 1; font-family: 'Inter', monospace;">${formatCurrency(totalIncome)}</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(16, 185, 129, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">💰</span>
                                                </div>
                                            </div>
                                            <div style="font-size: 0.75rem; color: #9ca3af;">
                                                ${monthlyIncome.length} transactions
                                            </div>
                                        </div>
                                        
                                        <!-- Total Expenses -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #ef4444; border-radius: 16px; padding: 1.5rem; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">TOTAL EXPENSES</h3>
                                                    <p style="font-size: 1.75rem; font-weight: 900; color: #ef4444; margin: 0; line-height: 1; font-family: 'Inter', monospace;">${formatCurrency(totalAllExpenses)}</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">💸</span>
                                                </div>
                                            </div>
                                            <div style="font-size: 0.75rem; color: #9ca3af;">
                                                ${monthlyExpenses.length + monthlyPacking.length} transactions
                                            </div>
                                        </div>
                                        
                                        <!-- Net Profit -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid ${netProfit >= 0 ? '#10b981' : '#ef4444'}; border-radius: 16px; padding: 1.5rem; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">NET PROFIT</h3>
                                                    <p style="font-size: 1.75rem; font-weight: 900; color: ${netProfit >= 0 ? '#10b981' : '#ef4444'}; margin: 0; line-height: 1; font-family: 'Inter', monospace;">${formatCurrency(netProfit)}</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(${netProfit >= 0 ? '16, 185, 129' : '239, 68, 68'}, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">${netProfit >= 0 ? '📈' : '📉'}</span>
                                                </div>
                                            </div>
                                            <div style="font-size: 0.75rem; color: #9ca3af;">
                                                ${profitMargin.toFixed(1)}% margin
                                            </div>
                                        </div>
                                        
                                        <!-- Total Transactions -->
                                        <div style="background: rgba(17, 17, 17, 0.8); border: 2px solid #3b82f6; border-radius: 16px; padding: 1.5rem; backdrop-filter: blur(10px);">
                                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                                <div>
                                                    <h3 style="font-size: 0.75rem; font-weight: 600; color: #9ca3af; text-transform: uppercase; letter-spacing: 0.1em; margin: 0 0 0.5rem 0;">TOTAL TRANSACTIONS</h3>
                                                    <p style="font-size: 1.75rem; font-weight: 900; color: #3b82f6; margin: 0; line-height: 1; font-family: 'Inter', monospace;">${totalTransactions}</p>
                                                </div>
                                                <div style="width: 32px; height: 32px; background: rgba(59, 130, 246, 0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <span style="font-size: 16px;">📊</span>
                                                </div>
                                            </div>
                                            <div style="font-size: 0.75rem; color: #9ca3af;">
                                                All categories
                                            </div>
                                        </div>
                                        
                                    </div>
                                    
                                    <!-- Performance Analysis -->
                                    <div style="position: relative; z-index: 2; margin-top: 2rem;">
                                        <h5 style="color: #ffffff; margin-bottom: 1.5rem; font-size: 1.25rem; font-weight: 700; display: flex; align-items: center; gap: 0.5rem;">
                                            <span>📈</span> Performance Analysis
                                        </h5>
                                        <div style="background: rgba(17, 17, 17, 0.6); padding: 2rem; border-radius: 16px; border: 1px solid rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
                                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                                                <div>
                                                    <p style="color: #9ca3af; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 600;">
                                                        <strong>💰 Income Breakdown:</strong>
                                                    </p>
                                                    <p style="color: #e5e7eb; margin-bottom: 0; font-size: 0.875rem;">
                                                        ${monthlyIncome.length} income transactions recorded
                                                    </p>
                                                </div>
                                                
                                                <div>
                                                    <p style="color: #9ca3af; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 600;">
                                                        <strong>💸 Expense Breakdown:</strong>
                                                    </p>
                                                    <p style="color: #e5e7eb; margin-bottom: 0; font-size: 0.875rem;">
                                                        ${monthlyExpenses.length} regular expenses, ${monthlyPacking.length} packing costs
                                                    </p>
                                                </div>
                                                
                                                <div>
                                                    <p style="color: #9ca3af; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 600;">
                                                        <strong>📦 Packing Costs:</strong>
                                                    </p>
                                                    <p style="color: #e5e7eb; margin-bottom: 0; font-size: 0.875rem;">
                                                        ${formatCurrency(totalPacking)} total packaging expenses
                                                    </p>
                                                </div>
                                                
                                                <div>
                                                    <p style="color: #9ca3af; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 600;">
                                                        <strong>📊 Performance:</strong>
                                                    </p>
                                                    <p style="color: #e5e7eb; margin-bottom: 0; font-size: 0.875rem;">
                                                        ${profitMargin > 20 ? '🎉 Excellent' : profitMargin > 10 ? '👍 Good' : profitMargin > 0 ? '⚠️ Fair' : '🚨 Needs Attention'} profit margin (${profitMargin.toFixed(1)}%)
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            ${totalTransactions === 0 ? `
                                                <div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: rgba(55, 65, 81, 0.3); border-radius: 12px; border: 1px dashed rgba(156, 163, 175, 0.3);">
                                                    <p style="color: #9ca3af; margin: 0; font-size: 0.9rem;">
                                                        📅 No transactions recorded for ${monthName} 2025
                                                    </p>
                                                </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                    
                                    <!-- Subtle animation glow -->
                                    <div style="position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(45deg, #3b82f6, #10b981, #f59e0b, #ef4444, #3b82f6); border-radius: 24px; z-index: -1; opacity: 0.1; animation: gradientShift 6s ease infinite; background-size: 400% 400%;"></div>
                                </div>
                `;
                
                console.log('✅ Monthly report generated successfully');
                
            } catch (error) {
                console.error('❌ Error generating monthly report:', error);
                contentDiv.innerHTML = `
                    <div style="padding: 2rem; text-align: center; color: var(--accent-danger);">
                        <p>❌ Error generating monthly report</p>
                        <small style="color: var(--text-muted);">Please try selecting the month again</small>
                    </div>
                `;
            }
        }

        // ============================================================================
        // FORM HANDLERS - TRANSACTIONS
        // ============================================================================

        function handleIncomeSubmit(e) {
            e.preventDefault();
            
            const category = document.getElementById('incomeCategory').value;
            const description = document.getElementById('incomeDescription').value;
            
            if (!category) {
                showNotification('⚠️ Please select a category!', 'error');
                return;
            }
            
            let formData = {
                id: Date.now(),
                date: document.getElementById('incomeDate').value,
                category: category,
                description: description
            };
            
            if (category === 'Product Sales') {
                const productId = parseInt(document.getElementById('incomeProduct').value);
                const qty = parseFloat(document.getElementById('incomeQty').value);
                const amount = parseFloat(document.getElementById('incomeAmount').value);
                
                if (!productId || !qty || !amount) {
                    showNotification('⚠️ Please fill all product fields!', 'error');
                    return;
                }
                
                const product = appData.products.find(p => p.id === productId);
                if (!product) {
                    showNotification('⚠️ Selected product not found!', 'error');
                    return;
                }
                
                const totalCost = product.cost * qty;
                const profit = amount - totalCost;
                const profitMargin = amount > 0 ? (profit / amount) * 100 : 0;
                
                formData = {
                    ...formData,
                    productId: productId,
                    productName: product.name,
                    qty: qty,
                    unitPrice: product.price,
                    amount: amount,
                    cost: totalCost,
                    profit: profit,
                    profitMargin: profitMargin,
                    description: description || `Sale of ${qty}x ${product.name}`
                };
                
            } else {
                const amount = parseFloat(document.getElementById('incomeAmountManual').value);
                
                if (!amount || amount <= 0) {
                    showNotification('⚠️ Please enter a valid amount!', 'error');
                    return;
                }
                
                formData = {
                    ...formData,
                    amount: amount,
                    profit: amount,
                    profitMargin: 100
                };
            }
            
            appData.transactions.income.push(formData);
            
            // Reset form
            e.target.reset();
            document.getElementById('incomeDate').value = new Date().toISOString().split('T')[0];
            
            // Reset product fields
            toggleProductFields();
            
            // Update dashboard if visible
            const dashboardSection = document.getElementById('dashboard');
            if (dashboardSection && dashboardSection.classList.contains('active')) {
                updateDashboard();
            }
            
            saveData();
            showNotification('✅ Income recorded successfully!', 'success');
            
            console.log('Income saved:', formData);
        }

        function handleExpenseSubmit(e) {
            e.preventDefault();
            
            const formData = {
                id: Date.now(),
                date: document.getElementById('expenseDate').value,
                category: document.getElementById('expenseCategory').value,
                amount: parseFloat(document.getElementById('expenseAmount').value),
                description: document.getElementById('expenseDescription').value
            };
            
            if (!formData.category) {
                showNotification('⚠️ Please select a category!', 'error');
                return;
            }
            
            if (!formData.amount || formData.amount <= 0) {
                showNotification('⚠️ Please enter a valid amount!', 'error');
                return;
            }
            
            appData.transactions.expense.push(formData);
            
            // Reset form
            e.target.reset();
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
            
            // Update dashboard if visible
            const dashboardSection = document.getElementById('dashboard');
            if (dashboardSection && dashboardSection.classList.contains('active')) {
                updateDashboard();
            }
            
            saveData();
            showNotification('✅ Expense recorded successfully!', 'success');
            
            console.log('Expense saved:', formData);
        }

        function handlePackingSubmit(e) {
            e.preventDefault();
            
            const formData = {
                id: Date.now(),
                date: document.getElementById('packingDate').value,
                item: document.getElementById('packingItem').value,
                qty: parseFloat(document.getElementById('packingQty').value),
                price: parseFloat(document.getElementById('packingPrice').value),
                total: parseFloat(document.getElementById('packingTotal').value)
            };
            
            if (!formData.item) {
                showNotification('⚠️ Please select a material!', 'error');
                return;
            }
            
            if (!formData.qty || formData.qty <= 0) {
                showNotification('⚠️ Please enter a valid quantity!', 'error');
                return;
            }
            
            if (!formData.price || formData.price <= 0) {
                showNotification('⚠️ Please enter a valid price!', 'error');
                return;
            }
            
            appData.transactions.packing.push(formData);
            
            // Reset form
            e.target.reset();
            document.getElementById('packingDate').value = new Date().toISOString().split('T')[0];
            
            // Update dashboard if visible
            const dashboardSection = document.getElementById('dashboard');
            if (dashboardSection && dashboardSection.classList.contains('active')) {
                updateDashboard();
            }
            
            saveData();
            showNotification('✅ Packing purchase recorded successfully!', 'success');
            
            console.log('Packing saved:', formData);
        }

        // ============================================================================
        // FORM HANDLERS - DATA MASTER
        // ============================================================================

        function handleMaterialSubmit(e) {
            e.preventDefault();
            
            const name = document.getElementById('newMaterialName').value.trim();
            if (!name) {
                showNotification('⚠️ Please enter material name!', 'error');
                return;
            }
            
            const exists = appData.packingItems.some(item => item.name.toLowerCase() === name.toLowerCase());
            if (exists) {
                showNotification('⚠️ Material already exists!', 'error');
                return;
            }
            
            const newId = Math.max(...appData.packingItems.map(item => item.id), 0) + 1;
            appData.packingItems.push({ id: newId, name: name });
            
            e.target.reset();
            loadMaterialsTable();
            updatePackingDropdowns();
            saveData();
            showNotification('✅ Material added successfully!', 'success');
        }

        function handleProductSubmit(e) {
            e.preventDefault();
            
            const name = document.getElementById('newProductName').value.trim();
            const cost = parseFloat(document.getElementById('newProductCost').value);
            const price = parseFloat(document.getElementById('newProductPrice').value);
            
            if (!name || cost < 0 || price < 0) {
                showNotification('⚠️ Please fill all fields correctly!', 'error');
                return;
            }
            
            if (price <= cost) {
                showNotification('⚠️ Selling price must be higher than cost!', 'error');
                return;
            }
            
            const product = {
                id: Date.now(),
                name: name,
                cost: cost,
                price: price,
                profit: price - cost,
                margin: ((price - cost) / price * 100)
            };
            
            appData.products.push(product);
            
            e.target.reset();
            loadProductsTable();
            updateProductDropdown();
            saveData();
            showNotification('✅ Product added successfully!', 'success');
        }

        function handleIncomeCategorySubmit(e) {
            e.preventDefault();

            const name = document.getElementById('newIncomeCategoryName').value.trim();
            if (!name) {
                showNotification('⚠️ Please enter category name!', 'error');
                return;
            }

            const exists = appData.incomeCategories.some(cat => cat.name.toLowerCase() === name.toLowerCase());
            if (exists) {
                showNotification('⚠️ Category already exists!', 'error');
                return;
            }

            const newId = Math.max(...appData.incomeCategories.map(cat => cat.id), 0) + 1;
            appData.incomeCategories.push({ id: newId, name: name });

            e.target.reset();
            loadIncomeCategoriesTable();
            updateCategoryDropdowns();
            saveData();
            showNotification('✅ Income category added successfully!', 'success');
        }

        function handleExpenseCategorySubmit(e) {
            e.preventDefault();

            const name = document.getElementById('newExpenseCategoryName').value.trim();
            if (!name) {
                showNotification('⚠️ Please enter category name!', 'error');
                return;
            }

            const exists = appData.expenseCategories.some(cat => cat.name.toLowerCase() === name.toLowerCase());
            if (exists) {
                showNotification('⚠️ Category already exists!', 'error');
                return;
            }

            const newId = Math.max(...appData.expenseCategories.map(cat => cat.id), 0) + 1;
            appData.expenseCategories.push({ id: newId, name: name });

            e.target.reset();
            loadExpenseCategoriesTable();
            updateCategoryDropdowns();
            saveData();
            showNotification('✅ Expense category added successfully!', 'success');
        }

        // ============================================================================
        // TABLE MANAGEMENT FUNCTIONS
        // ============================================================================

        function loadMaterialsTable() {
            const tbody = document.getElementById('materialsTable');
            if (!tbody) return;
            
            if (appData.packingItems.length === 0) {
                tbody.innerHTML = '<tr><td colspan="2" style="text-align: center; color: var(--text-muted);">No materials yet</td></tr>';
                return;
            }
            
            tbody.innerHTML = appData.packingItems.map(material => 
                `<tr>
                    <td style="font-weight: 600;">${material.name}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem;" onclick="deleteMaterial(${material.id})">Delete</button>
                    </td>
                </tr>`
            ).join('');
        }

        function loadProductsTable() {
            const tbody = document.getElementById('productsTable');
            if (!tbody) return;
            
            tbody.innerHTML = appData.products.map((product, index) => `
                <tr>
                    <td style="font-weight: 600;">${product.name}</td>
                    <td>${formatCurrency(product.cost)}</td>
                    <td>${formatCurrency(product.price)}</td>
                    <td style="color: var(--accent-secondary); font-weight: 600;">${formatCurrency(product.profit)}</td>
                    <td><span style="background: var(--gradient-secondary); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 600;">${product.margin.toFixed(1)}%</span></td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem;" onclick="deleteProduct(${index})">
                            Delete
                        </button>
                    </td>
                </tr>
            `).join('');
            
            if (appData.products.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No products yet</td></tr>';
            }
        }

        function loadIncomeCategoriesTable() {
            const tbody = document.getElementById('incomeCategoriesTable');
            if (!tbody) return;

            if (appData.incomeCategories.length === 0) {
                tbody.innerHTML = '<tr><td colspan="2" style="text-align: center; color: var(--text-muted);">No income categories yet</td></tr>';
                return;
            }

            tbody.innerHTML = appData.incomeCategories.map(category =>
                `<tr>
                    <td style="font-weight: 600;">${category.name}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem;" onclick="deleteIncomeCategory(${category.id})">Delete</button>
                    </td>
                </tr>`
            ).join('');
        }

        function loadExpenseCategoriesTable() {
            const tbody = document.getElementById('expenseCategoriesTable');
            if (!tbody) return;

            if (appData.expenseCategories.length === 0) {
                tbody.innerHTML = '<tr><td colspan="2" style="text-align: center; color: var(--text-muted);">No expense categories yet</td></tr>';
                return;
            }

            tbody.innerHTML = appData.expenseCategories.map(category =>
                `<tr>
                    <td style="font-weight: 600;">${category.name}</td>
                    <td>
                        <button class="btn btn-danger" style="padding: 0.5rem 1rem;" onclick="deleteExpenseCategory(${category.id})">Delete</button>
                    </td>
                </tr>`
            ).join('');
        }

        // ============================================================================
        // DELETE FUNCTIONS
        // ============================================================================

        function deleteMaterial(id) {
            if (confirm('Are you sure you want to delete this material?')) {
                appData.packingItems = appData.packingItems.filter(item => item.id !== id);
                loadMaterialsTable();
                updatePackingDropdowns();
                saveData();
                showNotification('✅ Material deleted successfully!', 'success');
            }
        }

        function deleteProduct(index) {
            if (confirm('Are you sure you want to delete this product?')) {
                appData.products.splice(index, 1);
                loadProductsTable();
                updateProductDropdown();
                saveData();
                showNotification('✅ Product deleted successfully!', 'success');
            }
        }

        function deleteIncomeCategory(id) {
            if (confirm('Are you sure you want to delete this income category?')) {
                appData.incomeCategories = appData.incomeCategories.filter(cat => cat.id !== id);
                loadIncomeCategoriesTable();
                updateCategoryDropdowns();
                saveData();
                showNotification('✅ Income category deleted successfully!', 'success');
            }
        }

        function deleteExpenseCategory(id) {
            if (confirm('Are you sure you want to delete this expense category?')) {
                appData.expenseCategories = appData.expenseCategories.filter(cat => cat.id !== id);
                loadExpenseCategoriesTable();
                updateCategoryDropdowns();
                saveData();
                showNotification('✅ Expense category deleted successfully!', 'success');
            }
        }

        // ============================================================================
        // DROPDOWN UPDATE FUNCTIONS
        // ============================================================================

        function updatePackingDropdowns() {
            const packingSelect = document.getElementById('packingItem');
            const filterSelect = document.getElementById('filterItem');
            
            if (packingSelect) {
                const currentValue = packingSelect.value;
                packingSelect.innerHTML = '<option value="">Select Material</option>' + 
                   appData.packingItems.map(item => '<option value="' + item.name + '">' + item.name + '</option>').join('');
                packingSelect.value = currentValue;
            }
            
            if (filterSelect) {
                const currentValue = filterSelect.value;
                filterSelect.innerHTML = '<option value="">All Materials</option>' + 
                    appData.packingItems.map(item => '<option value="' + item.name + '">' + item.name + '</option>').join('');
                filterSelect.value = currentValue;
            }
        }

        function updateProductDropdown() {
            const productSelect = document.getElementById('incomeProduct');
            if (!productSelect) return;
            
            const currentValue = productSelect.value;
            productSelect.innerHTML = '<option value="">Select Product</option>';
            
            if (appData.products.length === 0) {
                productSelect.innerHTML += '<option value="" disabled>No products available - Add products first</option>';
                return;
            }
            
            appData.products.forEach(product => {
                productSelect.innerHTML += `<option value="${product.id}">${product.name} - ${formatCurrency(product.price)}</option>`;
            });
            
            productSelect.value = currentValue;
        }

        function updateCategoryDropdowns() {
            // Update income category dropdown
            const incomeSelect = document.getElementById('incomeCategory');
            if (incomeSelect) {
                const currentValue = incomeSelect.value;
                incomeSelect.innerHTML = '<option value="">Select Category</option>';

                appData.incomeCategories.forEach(category => {
                    incomeSelect.innerHTML += `<option value="${category.name}">${category.name}</option>`;
                });

                incomeSelect.value = currentValue;
            }

            // Update expense category dropdown
            const expenseSelect = document.getElementById('expenseCategory');
            if (expenseSelect) {
                const currentValue = expenseSelect.value;
                expenseSelect.innerHTML = '<option value="">Select Category</option>';

                appData.expenseCategories.forEach(category => {
                    expenseSelect.innerHTML += `<option value="${category.name}">${category.name}</option>`;
                });

                expenseSelect.value = currentValue;
            }

            // Update history filter dropdown
            const historySelect = document.getElementById('historyCategoryFilter');
            if (historySelect) {
                const currentValue = historySelect.value;
                const allCategories = [
                    ...appData.incomeCategories.map(cat => cat.name),
                    ...appData.expenseCategories.map(cat => cat.name),
                    'Packing Materials'
                ];

                historySelect.innerHTML = '<option value="">All Categories</option>';
                [...new Set(allCategories)].sort().forEach(category => {
                    historySelect.innerHTML += `<option value="${category}">${category}</option>`;
                });

                historySelect.value = currentValue;
            }
        }

        // ============================================================================
        // CALCULATION FUNCTIONS
        // ============================================================================

        function calculateProductMargin() {
            const cost = parseFloat(document.getElementById('newProductCost').value) || 0;
            const price = parseFloat(document.getElementById('newProductPrice').value) || 0;
            const margin = price > 0 ? ((price - cost) / price * 100).toFixed(1) : 0;
            document.getElementById('newProductMargin').value = margin + '%';
        }

        function calculatePackingTotal() {
            const qtyEl = document.getElementById('packingQty');
            const priceEl = document.getElementById('packingPrice');
            const totalEl = document.getElementById('packingTotal');
            
            if (qtyEl && priceEl && totalEl) {
                const qty = parseFloat(qtyEl.value) || 0;
                const price = parseFloat(priceEl.value) || 0;
                const total = qty * price;
                
                totalEl.value = total;
            }
        }

        function calculateIncomeTotal() {
            const qty = parseFloat(document.getElementById('incomeQty').value) || 0;
            const price = parseFloat(document.getElementById('incomePrice').value) || 0;
            const total = qty * price;
            
            document.getElementById('incomeAmount').value = total;
        }

        // ============================================================================
        // PRODUCT MANAGEMENT FUNCTIONS
        // ============================================================================
        
        function toggleProductFields() {
            const category = document.getElementById('incomeCategory');
            if (!category) return;
            
            const categoryValue = category.value;
            const isProductSales = categoryValue === 'Product Sales';
            
            const productField = document.getElementById('productField');
            const qtyField = document.getElementById('qtyField');
            const priceField = document.getElementById('priceField');
            const totalField = document.getElementById('totalField');
            const manualAmountField = document.getElementById('manualAmountField');
            
            if (isProductSales) {
                if (productField) productField.style.display = 'block';
                if (qtyField) qtyField.style.display = 'block';
                if (priceField) priceField.style.display = 'block';
                if (totalField) totalField.style.display = 'block';
                if (manualAmountField) manualAmountField.style.display = 'none';
                
                updateProductDropdown();
                
                const productSelect = document.getElementById('incomeProduct');
                const qtyInput = document.getElementById('incomeQty');
                const manualInput = document.getElementById('incomeAmountManual');
                
                if (productSelect) productSelect.required = true;
                if (qtyInput) qtyInput.required = true;
                if (manualInput) manualInput.required = false;
                
                if (manualInput) manualInput.value = '';
                
            } else if (categoryValue) {
                if (productField) productField.style.display = 'none';
                if (qtyField) qtyField.style.display = 'none';
                if (priceField) priceField.style.display = 'none';
                if (totalField) totalField.style.display = 'none';
                if (manualAmountField) manualAmountField.style.display = 'block';
                
                const productSelect = document.getElementById('incomeProduct');
                const qtyInput = document.getElementById('incomeQty');
                const manualInput = document.getElementById('incomeAmountManual');
                
                if (productSelect) productSelect.required = false;
                if (qtyInput) qtyInput.required = false;
                if (manualInput) manualInput.required = true;
                
                const productInputs = ['incomeProduct', 'incomeQty', 'incomePrice', 'incomeAmount'];
                productInputs.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.value = '';
                });
                
            } else {
                if (productField) productField.style.display = 'none';
                if (qtyField) qtyField.style.display = 'none';
                if (priceField) priceField.style.display = 'none';
                if (totalField) totalField.style.display = 'none';
                if (manualAmountField) manualAmountField.style.display = 'none';
                
                const allInputs = ['incomeProduct', 'incomeQty', 'incomePrice', 'incomeAmount', 'incomeAmountManual'];
                allInputs.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.value = '';
                });
            }
        }

        function selectProduct() {
            const productSelect = document.getElementById('incomeProduct');
            const priceInput = document.getElementById('incomePrice');
            const selectedProductId = parseInt(productSelect.value);
            
            if (selectedProductId) {
                const product = appData.products.find(p => p.id === selectedProductId);
                if (product) {
                    priceInput.value = product.price;
                    calculateIncomeTotal();
                }
            } else {
                priceInput.value = '';
                document.getElementById('incomeAmount').value = '';
            }
        }

        // ============================================================================
        // DASHBOARD & UTILITY FUNCTIONS
        // ============================================================================

        function updateDashboard() {
            updateStats();
            updateCharts();
            updateAIInsights();
            console.log('Dashboard updated with latest data');
        }

        function updateStats() {
            const totalRevenue = appData.transactions.income.reduce((sum, t) => sum + t.amount, 0);
            const totalExpense = appData.transactions.expense.reduce((sum, t) => sum + t.amount, 0);
            const totalPacking = appData.transactions.packing.reduce((sum, t) => sum + t.total, 0);
            const totalExpenses = totalExpense + totalPacking;
            const netProfit = totalRevenue - totalExpenses;
            
            const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;
            
            // Update dashboard elements if they exist
            const elements = {
                totalRevenueDash: formatCurrency(totalRevenue),
                totalExpenseDash: formatCurrency(totalExpenses),
                netProfitDash: formatCurrency(netProfit),
                profitMarginDash: profitMargin.toFixed(1) + '%'
            };
            
            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    
                    // Apply color coding for profit/loss
                    if (id === 'netProfitDash') {
                        element.style.color = netProfit >= 0 ? 'var(--accent-secondary)' : 'var(--accent-danger)';
                    }
                    if (id === 'profitMarginDash') {
                        if (profitMargin > 30) element.style.color = 'var(--accent-secondary)';
                        else if (profitMargin > 15) element.style.color = 'var(--accent-warning)';
                        else if (profitMargin > 0) element.style.color = 'var(--accent-warning)';
                        else element.style.color = 'var(--accent-danger)';
                    }
                }
            });
            
            // Update change indicators with better logic
            const revenueChange = document.getElementById('revenueChange');
            const expenseChange = document.getElementById('expenseChange');
            const profitChange = document.getElementById('profitChange');
            const marginChange = document.getElementById('marginChange');
            
            if (revenueChange) {
                if (totalRevenue > 0) {
                    revenueChange.textContent = `+${((totalRevenue / Math.max(totalRevenue, 1000)) * 100).toFixed(1)}%`;
                    revenueChange.className = 'stat-change positive';
                } else {
                    revenueChange.textContent = '0%';
                    revenueChange.className = 'stat-change';
                }
            }
            
            if (expenseChange) {
                if (totalExpenses > 0) {
                    expenseChange.textContent = `+${((totalExpenses / Math.max(totalExpenses, 1000)) * 100).toFixed(1)}%`;
                    expenseChange.className = 'stat-change negative';
                } else {
                    expenseChange.textContent = '0%';
                    expenseChange.className = 'stat-change';
                }
            }
            
            if (profitChange) {
                if (netProfit > 0) {
                    profitChange.textContent = `+${((Math.abs(netProfit) / Math.max(totalRevenue, 1)) * 100).toFixed(1)}%`;
                    profitChange.className = 'stat-change positive';
                } else if (netProfit < 0) {
                    profitChange.textContent = `-${((Math.abs(netProfit) / Math.max(totalRevenue, 1)) * 100).toFixed(1)}%`;
                    profitChange.className = 'stat-change negative';
                } else {
                    profitChange.textContent = '0%';
                    profitChange.className = 'stat-change';
                }
            }
            
            if (marginChange) {
                if (profitMargin > 30) {
                    marginChange.textContent = 'Excellent';
                    marginChange.className = 'stat-change positive';
                } else if (profitMargin > 15) {
                    marginChange.textContent = 'Good';
                    marginChange.className = 'stat-change positive';
                } else if (profitMargin > 5) {
                    marginChange.textContent = 'Fair';
                    marginChange.className = 'stat-change';
                } else if (profitMargin > 0) {
                    marginChange.textContent = 'Low';
                    marginChange.className = 'stat-change negative';
                } else {
                    marginChange.textContent = 'Loss';
                    marginChange.className = 'stat-change negative';
                }
            }
            
            console.log('Stats updated:', { totalRevenue, totalExpenses, netProfit, profitMargin });
        }

        function updateCharts() {
            updateMonthlyChart();

            // Only update expense chart if dashboard is visible
            const dashboardTab = document.querySelector('[data-tab="dashboard"]');
            const isDashboardActive = dashboardTab && dashboardTab.classList.contains('active');

            if (isDashboardActive) {
                updateExpenseChart();
            }
        }

        function updateMonthlyChart() {
            const ctx = document.getElementById('monthlyChart');
            if (!ctx) {
                console.warn('❌ Monthly chart canvas not found');
                return;
            }
            
            try {
                // Destroy existing chart
                if (monthlyChart) {
                    monthlyChart.destroy();
                    monthlyChart = null;
                }
                
                // Prepare data for the last 6 months
                const months = [];
                const incomeData = [];
                const expenseData = [];
                
                for (let i = 5; i >= 0; i--) {
                    const date = new Date();
                    date.setMonth(date.getMonth() - i);
                    const monthKey = date.getMonth() + 1;
                    const monthName = date.toLocaleDateString('en-US', { month: 'short' });
                    
                    months.push(monthName);
                    
                    const monthIncome = appData.transactions.income
                        .filter(t => {
                            try {
                                const transactionDate = new Date(t.date);
                                return transactionDate.getMonth() + 1 === monthKey;
                            } catch {
                                return false;
                            }
                        })
                        .reduce((sum, t) => sum + (t.amount || 0), 0);
                    
                    const monthExpenses = appData.transactions.expense
                        .filter(t => {
                            try {
                                const transactionDate = new Date(t.date);
                                return transactionDate.getMonth() + 1 === monthKey;
                            } catch {
                                return false;
                            }
                        })
                        .reduce((sum, t) => sum + (t.amount || 0), 0);
                    
                    const monthPacking = appData.transactions.packing
                        .filter(t => {
                            try {
                                const transactionDate = new Date(t.date);
                                return transactionDate.getMonth() + 1 === monthKey;
                            } catch {
                                return false;
                            }
                        })
                        .reduce((sum, t) => sum + (t.total || 0), 0);
                    
                    incomeData.push(monthIncome);
                    expenseData.push(monthExpenses + monthPacking);
                }
                
                console.log('📊 Monthly chart data:', { months, incomeData, expenseData });
                
                monthlyChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: months,
                        datasets: [{
                            label: 'Revenue',
                            data: incomeData,
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7
                        }, {
                            label: 'Expenses',
                            data: expenseData,
                            borderColor: '#ef4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#ef4444',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 7
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#e5e5e5',
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        size: 12,
                                        weight: '600'
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#374151',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#a1a1aa',
                                    callback: function(value) {
                                        return new Intl.NumberFormat('id-ID', {
                                            style: 'currency',
                                            currency: 'IDR',
                                            minimumFractionDigits: 0,
                                            maximumFractionDigits: 0
                                        }).format(value);
                                    }
                                },
                                grid: {
                                    color: '#374151',
                                    drawBorder: false
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#a1a1aa'
                                },
                                grid: {
                                    color: '#374151',
                                    drawBorder: false
                                }
                            }
                        }
                    }
                });
                
                console.log('✅ Monthly chart created successfully');
                
            } catch (error) {
                console.error('❌ Error creating monthly chart:', error);
                const chartContainer = ctx.closest('[style*="height: 350px"]') || ctx.parentElement;
                if (chartContainer) {
                    chartContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 350px; color: var(--accent-danger); font-size: 0.9rem;">❌ Error loading chart</div>';
                }
            }
        }

        function updateExpenseChart() {
            // Wait for DOM to be ready
            setTimeout(() => {
                const ctx = document.getElementById('expenseChart');
                if (!ctx) {
                    console.log('⚠️ Expense chart canvas not found, skipping chart update');
                    return;
                }

                createExpenseChart(ctx);
            }, 100);
        }

        function createExpenseChart(ctx) {
            try {
                // Destroy existing chart
                if (expenseChart) {
                    expenseChart.destroy();
                    expenseChart = null;
                }
                
                // Calculate expense categories
                const categoryData = {};
                
                // Process regular expenses
                appData.transactions.expense.forEach(transaction => {
                    const category = transaction.category || 'Other';
                    categoryData[category] = (categoryData[category] || 0) + transaction.amount;
                });
                
                // Add packing expenses
                const packingTotal = appData.transactions.packing.reduce((sum, t) => sum + t.total, 0);
                if (packingTotal > 0) {
                    categoryData['Packing Materials'] = packingTotal;
                }
                
                const labels = Object.keys(categoryData);
                const data = Object.values(categoryData);
                const colors = [
                    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
                    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
                ];
                
                console.log('📊 Expense chart data:', { labels, data });
                
                if (labels.length === 0) {
                    // Show empty chart message - fix the container reference
                    const chartContainer = ctx.closest('[style*="height: 350px"]') || ctx.parentElement;
                    if (chartContainer) {
                        chartContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 350px; color: var(--text-muted); font-size: 0.9rem;">📊 No expense data available<br><small>Add some expenses to see the distribution</small></div>';
                    }
                    return;
                }
                
                // Create new chart
                expenseChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: colors.slice(0, labels.length),
                            borderWidth: 2,
                            borderColor: '#1e1e1e',
                            hoverBorderWidth: 3,
                            hoverBorderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#e5e5e5',
                                    padding: 15,
                                    usePointStyle: true,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#374151',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = formatCurrency(context.raw);
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.raw / total) * 100).toFixed(1);
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        elements: {
                            arc: {
                                borderWidth: 2
                            }
                        },
                        cutout: '60%'
                    }
                });
                
                console.log('✅ Expense chart created successfully');
                
            } catch (error) {
                console.error('❌ Error creating expense chart:', error);
                const chartContainer = ctx.closest('[style*="height: 350px"]') || ctx.parentElement;
                if (chartContainer) {
                    chartContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 350px; color: var(--accent-danger); font-size: 0.9rem;">❌ Error loading chart</div>';
                }
            }
        }

        function updateAIInsights() {
            const insightsContainer = document.getElementById('aiInsights');
            if (!insightsContainer) return;
            
            const totalTransactions = appData.transactions.income.length + 
                                    appData.transactions.expense.length + 
                                    appData.transactions.packing.length;
            
            const totalRevenue = appData.transactions.income.reduce((sum, t) => sum + t.amount, 0);
            const totalExpenses = appData.transactions.expense.reduce((sum, t) => sum + t.amount, 0) +
                                 appData.transactions.packing.reduce((sum, t) => sum + t.total, 0);
            const netProfit = totalRevenue - totalExpenses;
            const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;
            
            let insights = [];
            
            if (totalTransactions === 0) {
                insights.push('💡 Welcome to FinancePro! Start adding transactions to get personalized business insights and recommendations.');
            } else {
                insights.push(`📊 You have recorded ${totalTransactions} transactions with a total revenue of ${formatCurrency(totalRevenue)}.`);
                
                if (profitMargin > 30) {
                    insights.push('🎉 Excellent profit margin! Your business is performing very well.');
                } else if (profitMargin > 15) {
                    insights.push('👍 Good profit margin. Consider optimizing expenses to increase profitability.');
                } else if (profitMargin > 5) {
                    insights.push('⚠️ Fair profit margin. Review your cost structure and pricing strategy.');
                } else if (profitMargin > 0) {
                    insights.push('📈 Low profit margin. Focus on reducing costs and increasing revenue.');
                } else {
                    insights.push('🚨 Operating at a loss. Immediate action needed to improve profitability.');
                }
                
                if (appData.products.length === 0) {
                    insights.push('💼 Add products in Data Master to track product-specific profitability.');
                }
                
                const highestExpenseCategory = getHighestExpenseCategory();
                if (highestExpenseCategory) {
                    insights.push(`💰 Highest expense category: ${highestExpenseCategory.name} (${formatCurrency(highestExpenseCategory.amount)})`);
                }
            }
            
            insightsContainer.innerHTML = insights.map(insight => 
                `<div style="padding: 1.5rem; background: var(--bg-tertiary); border-radius: 12px; margin-bottom: 1rem; border-left: 4px solid var(--accent-primary);">
                    <p style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6; margin: 0;">${insight}</p>
                </div>`
            ).join('');
        }

        function getHighestExpenseCategory() {
            const categoryTotals = {};
            
            appData.transactions.expense.forEach(transaction => {
                const category = transaction.category || 'Other';
                categoryTotals[category] = (categoryTotals[category] || 0) + transaction.amount;
            });
            
            const packingTotal = appData.transactions.packing.reduce((sum, t) => sum + t.total, 0);
            if (packingTotal > 0) {
                categoryTotals['Packing Materials'] = packingTotal;
            }
            
            if (Object.keys(categoryTotals).length === 0) return null;
            
            const highest = Object.entries(categoryTotals).reduce((max, [name, amount]) => 
                amount > max.amount ? { name, amount } : max, { name: '', amount: 0 });
            
            return highest.amount > 0 ? highest : null;
        }

        function backupData() {
            try {
                const dataToBackup = {
                    timestamp: new Date().toISOString(),
                    version: '2.0',
                    data: appData
                };
                
                const dataStr = JSON.stringify(dataToBackup, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
                
                const exportFileDefaultName = `financepro-backup-${new Date().toISOString().split('T')[0]}.json`;
                
                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();
                
                showNotification('✅ Data backed up successfully!', 'success');
            } catch (error) {
                console.error('Error backing up data:', error);
                showNotification('❌ Failed to backup data!', 'error');
            }
        }

        function restoreData(input) {
            try {
                const file = input.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const backup = JSON.parse(e.target.result);
                        
                        if (backup.data && backup.data.products && backup.data.transactions) {
                            if (confirm('Are you sure you want to replace all current data with this backup? Current data will be lost!')) {
                                appData = backup.data;
                                
                                loadProductsTable();
                                loadMaterialsTable();
                                loadIncomeCategoriesTable();
                                loadExpenseCategoriesTable();
                                updatePackingDropdowns();
                                updateProductDropdown();
                                updateCategoryDropdowns();
                                saveData();
                                
                                showNotification('✅ Data restored successfully!', 'success');
                            }
                        } else {
                            showNotification('❌ Invalid backup format!', 'error');
                        }
                    } catch (error) {
                        console.error('Error parsing backup:', error);
                        showNotification('❌ Backup file is corrupted!', 'error');
                    }
                };
                reader.readAsText(file);
                
                input.value = '';
            } catch (error) {
                console.error('Error restoring data:', error);
                showNotification('❌ Failed to restore data!', 'error');
            }
        }

        function resetAllData() {
            if (confirm('WARNING: Are you sure you want to delete ALL data? This action cannot be undone!')) {
                if (confirm('Final confirmation - ALL data will be permanently deleted!')) {
                    appData = {
                        products: [],
                        incomeCategories: ['Product Sales', 'Consulting Services', 'Affiliate Commissions', 'Passive Income', 'Other'],
                        expenseCategories: ['Raw Materials', 'Office Operations', 'Marketing & Promotion', 'Transport & Logistics', 'Maintenance', 'Other'],
                        packingItems: [
                            { id: 1, name: 'Small Box' },
                            { id: 2, name: 'Medium Box' },
                            { id: 3, name: 'Large Box' },
                            { id: 4, name: 'Bubble Wrap' },
                            { id: 5, name: 'White Tape' },
                            { id: 6, name: 'Fragile Tape' },
                            { id: 7, name: 'Plastic Wrap' },
                            { id: 8, name: 'Address Labels' },
                            { id: 9, name: 'Brand Stickers' }
                        ],
                        transactions: {
                            income: [],
                            expense: [],
                            packing: []
                        }
                    };
                    
                    localStorage.removeItem('financeProData');
                    saveData();
                    
                    loadProductsTable();
                    loadMaterialsTable();
                    loadIncomeCategoriesTable();
                    loadExpenseCategoriesTable();
                    updatePackingDropdowns();
                    updateProductDropdown();
                    updateCategoryDropdowns();
                    
                    showNotification('✅ All data has been reset!', 'success');
                }
            }
        }

        // ============================================================================
        // REGISTRATION FORM HANDLERS
        // ============================================================================
        
        async function handleRegistrationSubmit(e) {
            e.preventDefault();

            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const termsAccepted = document.getElementById('termsCheckbox').checked;

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Basic validation
            if (!fullName || !email || !password || !confirmPassword) {
                showNotification('⚠️ Please fill all fields!', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showNotification('⚠️ Passwords do not match!', 'error');
                return;
            }

            if (!termsAccepted) {
                showNotification('⚠️ Please accept the terms and conditions!', 'error');
                return;
            }

            submitBtn.innerHTML = '🔄 Creating Account...';
            submitBtn.disabled = true;

            try {
                // Try Firebase Auth registration
                if (window.auth && window.firebaseAuth) {
                    try {
                        const userCredential = await window.firebaseAuth.createUserWithEmailAndPassword(window.auth, email, password);
                        const user = userCredential.user;

                        // Update user profile with display name
                        await window.firebaseAuth.updateProfile(user, {
                            displayName: fullName
                        });

                        localStorage.setItem('userData', JSON.stringify({
                            name: fullName,
                            email: user.email,
                            uid: user.uid
                        }));
                        localStorage.setItem('isLoggedIn', 'true');

                        showNotification('🎉 Registration successful! Welcome to FinancePro!', 'success');

                        // Show success message
                        document.getElementById('registerForm').style.display = 'none';
                        document.getElementById('successMessage').style.display = 'block';

                        startCountdown();
                        return;

                    } catch (firebaseError) {
                        console.error('Firebase registration failed:', firebaseError.message);
                        showNotification(`❌ Registration failed: ${firebaseError.message}`, 'error');
                        return;
                    }
                }

                // Fallback to demo registration
                localStorage.setItem('userData', JSON.stringify({
                    name: fullName,
                    email: email,
                    uid: 'demo_user_' + Date.now()
                }));
                localStorage.setItem('isLoggedIn', 'true');

                showNotification('🎉 Registration successful! Welcome to FinancePro!', 'success');

                // Show success message
                document.getElementById('registerForm').style.display = 'none';
                document.getElementById('successMessage').style.display = 'block';

                startCountdown();

            } catch (error) {
                console.error('Registration error:', error);
                showNotification('❌ Registration failed: ' + error.message, 'error');
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        function setupRegistrationValidation() {
            // Password strength validation
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('passwordStrength');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            if (passwordInput && strengthIndicator) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    const strength = calculatePasswordStrength(password);
                    
                    strengthIndicator.style.display = password ? 'block' : 'none';
                    
                    if (password) {
                        strengthFill.style.width = strength.percentage + '%';
                        strengthFill.className = 'strength-fill strength-' + strength.level;
                        strengthText.textContent = strength.text;
                    }
                });
            }
            
            // Confirm password validation
            const confirmInput = document.getElementById('confirmPassword');
            if (confirmInput) {
                confirmInput.addEventListener('input', function() {
                    const password = document.getElementById('password').value;
                    const confirm = this.value;
                    
                    if (confirm) {
                        if (password === confirm) {
                            this.classList.remove('error');
                            this.classList.add('success');
                        } else {
                            this.classList.remove('success');
                            this.classList.add('error');
                        }
                    } else {
                        this.classList.remove('error', 'success');
                    }
                });
            }
        }

        function calculatePasswordStrength(password) {
            let score = 0;
            let feedback = [];
            
            if (password.length >= 8) score += 25;
            else feedback.push('at least 8 characters');
            
            if (/[a-z]/.test(password)) score += 25;
            else feedback.push('lowercase letter');
            
            if (/[A-Z]/.test(password)) score += 25;
            else feedback.push('uppercase letter');
            
            if (/[0-9]/.test(password)) score += 25;
            else feedback.push('number');
            
            let level, text;
            if (score < 50) {
                level = 'weak';
                text = 'Weak - Add ' + feedback.slice(0, 2).join(', ');
            } else if (score < 75) {
                level = 'medium';
                text = 'Fair - Add ' + feedback.join(', ');
            } else if (score < 100) {
                level = 'medium';
                text = 'Good - Add ' + feedback.join(', ');
            } else {
                level = 'strong';
                text = 'Strong password';
            }
            
            return { percentage: score, level, text };
        }

        // ============================================================================
        // INITIALIZATION FUNCTIONS
        // ============================================================================
        
        function initializeApp() {
            try {
                console.log('🚀 Initializing FinancePro...');
                
                initializeTheme();
                updateUserInfo();
                
                // Load data first
                loadData().then(() => {
                    console.log('📊 Data loaded in initializeApp');
                    
                    // Initialize tables and dropdowns after data is loaded
                    loadProductsTable();
                    loadMaterialsTable();
                    loadIncomeCategoriesTable();
                    loadExpenseCategoriesTable();
                    updatePackingDropdowns();
                    updateProductDropdown();
                    updateCategoryDropdowns();
                    
                    // Update dashboard with current data
                    updateDashboard();
                });
                
                // Setup mobile responsive behavior
                setupResponsiveHandlers();
                
                console.log('✅ FinancePro initialized successfully!');
                
                setTimeout(() => {
                    showNotification('🎉 Welcome to FinancePro! Your business intelligence platform is ready.', 'success');
                }, 1500);
                
            } catch (error) {
                console.error('❌ Error initializing app:', error);
                showNotification('⚠️ Error initializing app. Please refresh the page.', 'error');
            }
        }

        function updateUserInfo() {
            try {
                let userData = {};
                try {
                    userData = JSON.parse(localStorage.getItem('userData') || '{}');
                } catch (parseError) {
                    console.log('⚠️ userData corrupted, using defaults');
                    userData = {};
                }

                const userNameEl = document.querySelector('.user-name');
                const userEmailEl = document.querySelector('.user-email');

                if (userNameEl && userEmailEl) {
                    userNameEl.textContent = userData.name || 'Demo Admin';
                    userEmailEl.textContent = userData.email || '<EMAIL>';
                }
            } catch (error) {
                console.log('Using default user info');
            }
        }

        function handleQuickAdd() {
            // Detect current active section
            const activeSection = document.querySelector('.content-section.active');
            if (!activeSection) {
                showSection('input');
                return;
            }
            
            const sectionId = activeSection.id;
            
            switch (sectionId) {
                case 'tracking':
                    // Switch to packing input tab
                    showSection('input');
                    setTimeout(() => showInputTab('packing'), 100);
                    break;
                case 'reports':
                case 'history':
                    // Go to input section
                    showSection('input');
                    break;
                case 'settings':
                    // Add new product
                    showSection('settings');
                    setTimeout(() => {
                        showSettingTab('products');
                        const productNameInput = document.getElementById('newProductName');
                        if (productNameInput) productNameInput.focus();
                    }, 100);
                    break;
                default:
                    // Default to input section
                    showSection('input');
                    break;
            }
        }

        function exportPackingData() {
            try {
                if (appData.transactions.packing.length === 0) {
                    showNotification('⚠️ No packing data to export', 'warning');
                    return;
                }
                
                const csvData = [
                    ['Date', 'Material', 'Quantity', 'Unit Price', 'Total Amount'],
                    ...appData.transactions.packing.map(t => [
                        t.date,
                        t.item,
                        t.qty,
                        t.price,
                        t.total
                    ])
                ];
                
                const csvContent = csvData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `packing-materials-${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                
                window.URL.revokeObjectURL(url);
                showNotification('✅ Packing data exported successfully!', 'success');
                
            } catch (error) {
                console.error('Export error:', error);
                showNotification('❌ Export failed', 'error');
            }
        }

        function setupResponsiveHandlers() {
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', (e) => {
                const sidebar = document.getElementById('sidebar');
                const menuBtn = document.querySelector('.mobile-menu-btn');
                
                if (window.innerWidth <= 768 && 
                    sidebar && sidebar.classList.contains('mobile-show') &&
                    !sidebar.contains(e.target) && 
                    !menuBtn.contains(e.target)) {
                    
                    toggleSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');
                
                if (window.innerWidth > 768) {
                    if (sidebar) sidebar.classList.remove('mobile-show');
                    if (overlay) overlay.remove();
                }
            });
        }

        // ============================================================================
        // EVENT LISTENERS & INITIALIZATION
        // ============================================================================
        
        document.addEventListener('DOMContentLoaded', function() {
            // Setup Firebase Auth State Listener
            if (window.auth && window.firebaseAuth) {
                window.firebaseAuth.onAuthStateChanged(window.auth, (user) => {
                    if (user) {
                        console.log('🔥 Firebase user authenticated:', user.email);
                        localStorage.setItem('userData', JSON.stringify({
                            name: user.displayName || user.email.split('@')[0],
                            email: user.email,
                            uid: user.uid
                        }));
                        localStorage.setItem('isLoggedIn', 'true');
                    } else {
                        console.log('🔥 Firebase user signed out');
                    }
                });
            }

            // Setup login form
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
                console.log('✅ Login form listener attached');
            }

            // Setup registration form
            const registrationForm = document.getElementById('registrationForm');
            if (registrationForm) {
                registrationForm.addEventListener('submit', handleRegistrationSubmit);
                setupRegistrationValidation();
                console.log('✅ Registration form listener attached');
            }

            // Setup Data Master forms
            const productForm = document.getElementById('productForm');
            const materialForm = document.getElementById('materialForm');
            const incomeCategoryForm = document.getElementById('incomeCategoryForm');
            const expenseCategoryForm = document.getElementById('expenseCategoryForm');

            if (productForm) {
                productForm.addEventListener('submit', handleProductSubmit);
                console.log('✅ Product form listener attached');
            }

            if (materialForm) {
                materialForm.addEventListener('submit', handleMaterialSubmit);
                console.log('✅ Material form listener attached');
            }

            if (incomeCategoryForm) {
                incomeCategoryForm.addEventListener('submit', handleIncomeCategorySubmit);
                console.log('✅ Income category form listener attached');
            }

            if (expenseCategoryForm) {
                expenseCategoryForm.addEventListener('submit', handleExpenseCategorySubmit);
                console.log('✅ Expense category form listener attached');
            }

            // Setup Transaction forms
            const incomeForm = document.getElementById('incomeForm');
            const expenseForm = document.getElementById('expenseForm');
            const packingForm = document.getElementById('packingForm');

            if (incomeForm) {
                incomeForm.addEventListener('submit', handleIncomeSubmit);
                console.log('✅ Income form listener attached');
            }
            
            if (expenseForm) {
                expenseForm.addEventListener('submit', handleExpenseSubmit);
                console.log('✅ Expense form listener attached');
            }
            
            if (packingForm) {
                packingForm.addEventListener('submit', handlePackingSubmit);
                console.log('✅ Packing form listener attached');
            }

            // Load existing data
            loadData().then(() => {
                console.log('✅ Data loaded successfully');
                // Initialize tables after data is loaded
                setTimeout(() => {
                    loadProductsTable();
                    loadMaterialsTable();
                    loadIncomeCategoriesTable();
                    loadExpenseCategoriesTable();
                    updatePackingDropdowns();
                    updateProductDropdown();
                    updateCategoryDropdowns();
                }, 100);
            });

            // Check authentication state
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userData = localStorage.getItem('userData');
            
            if (isLoggedIn === 'true' && userData) {
                document.getElementById('loginContainer').classList.add('hidden');
                document.getElementById('appContainer').classList.add('visible');
                
                setTimeout(() => {
                    initializeApp();
                    console.log('👤 User pre-authenticated, initializing...');
                }, 100);
            }
            
            // Initialize theme
            initializeTheme();
            
            // Set default dates
            const today = new Date().toISOString().split('T')[0];
            const dateInputs = ['incomeDate', 'expenseDate', 'packingDate'];
            dateInputs.forEach(id => {
                const input = document.getElementById(id);
                if (input) input.value = today;
            });

            console.log('✅ FinancePro loaded successfully!');
        });

        // Window events
        window.addEventListener('load', function() {
            console.log('🌟 FinancePro - Business Intelligence Platform');
            console.log('💼 Built with premium UI/UX and advanced analytics');
            console.log('📊 Powered by Chart.js & Advanced Local Storage');
        });
        
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');
                const menuBtn = document.querySelector('.mobile-menu-btn');
                
                if (window.innerWidth <= 768) {
                    if (menuBtn) menuBtn.style.display = 'block';
                } else {
                    if (menuBtn) menuBtn.style.display = 'none';
                    if (sidebar) sidebar.classList.remove('mobile-show');
                    if (overlay) overlay.remove();
                }
            }, 100);
        });
    </script>
</body>
</html>